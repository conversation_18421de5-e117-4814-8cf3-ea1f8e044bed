# إدارة المنتجات للوكلاء - Item Management for Wholesalers

## نظرة عامة - Overview

تم تطوير نظام شامل لإدارة المنتجات للوكلاء يتضمن عمليات CRUD كاملة، إدارة المخزون، والبحث المتقدم مع واجهة مستخدم باللغة العربية.

A comprehensive item management system for wholesalers has been implemented with full CRUD operations, inventory management, and advanced search with Arabic UI.

## الميزات المطبقة - Implemented Features

### 1. إدارة المنتجات الأساسية - Core Item Management
- ✅ إضافة منتجات جديدة مع اختيار الشركة والمنتج
- ✅ عرض قائمة المنتجات مع جدول تفاعلي
- ✅ تعديل بيانات المنتجات والأسعار
- ✅ حذف المنتجات (حذف ناعم)
- ✅ البحث والتصفية المتقدمة

### 2. إدارة المخزون - Inventory Management
- ✅ تتبع مستويات المخزون
- ✅ إضافة وخصم الكميات
- ✅ مؤشرات حالة المخزون (متوفر، منخفض، نفد)
- ✅ سجل معاملات المخزون مع الملاحظات

### 3. واجهة المستخدم - User Interface
- ✅ تصميم متجاوب يدعم جميع أحجام الشاشات
- ✅ نصوص باللغة العربية بالكامل
- ✅ نوافذ حوارية للإنشاء والتعديل
- ✅ إشعارات نجاح وخطأ
- ✅ مؤشرات التحميل

### 4. التكامل مع API - API Integration
- ✅ استخدام v2 API للقراءة حيث أمكن
- ✅ استخدام v1 API لعمليات CRUD
- ✅ معالجة الأخطاء والمصادقة
- ✅ إدارة حالة التطبيق مع React Query

## هيكل الملفات - File Structure

```
frontend/app/routes/items/
├── items.tsx                    # الصفحة الرئيسية
└── components/
    ├── ItemTable.tsx           # جدول المنتجات
    ├── ItemCreateDialog.tsx    # نافذة إضافة منتج
    ├── ItemEditDialog.tsx      # نافذة تعديل منتج
    ├── InventoryUpdateDialog.tsx # نافذة تحديث المخزون
    └── ProductSearchField.tsx  # حقل البحث عن المنتجات
```

## API Endpoints المستخدمة - Used API Endpoints

### V2 API (Read Operations)
- `GET /api/v2/categories-companies/` - جلب الشركات والفئات
- `GET /api/v2/home/<USER>

### V1 API (CRUD Operations)
- `GET /api/wholesalers/me/items` - جلب منتجات الوكيل
- `POST /api/wholesalers/me/items` - إضافة منتج جديد
- `PUT /api/wholesalers/me/items/{id}` - تحديث منتج
- `DELETE /api/wholesalers/me/items/{id}` - حذف منتج
- `POST /api/wholesalers/me/items/{id}/inventory` - تحديث المخزون

## المكونات الرئيسية - Key Components

### ItemTable
- عرض المنتجات في جدول تفاعلي
- أعمدة: المنتج، الباركود، السعر، المخزون، الحدود، التاريخ
- قائمة إجراءات لكل منتج (تعديل، تحديث مخزون، حذف)
- مؤشرات حالة المخزون بالألوان

### ItemCreateDialog
- نافذة حوارية لإضافة منتج جديد
- بحث ذكي في المنتجات مع تصفية بالشركة
- تحقق من صحة البيانات
- معاينة المنتج المختار

### ProductSearchField
- حقل بحث متقدم مع اقتراحات
- تصفية بالشركة
- عرض تفاصيل المنتج (صورة، اسم، باركود، شركة)
- دعم البحث النصي

### InventoryUpdateDialog
- إضافة أو خصم كميات من المخزون
- معاينة المخزون الجديد قبل التأكيد
- إضافة ملاحظات للمعاملة
- مؤشرات بصرية للتغييرات

## التنقل - Navigation

تم تحديث الشريط الجانبي ليشمل:
- لوحة التحكم
- إدارة المنتجات (الصفحة الحالية)
- الطلبات (للتطوير المستقبلي)
- التقارير (للتطوير المستقبلي)
- الإعدادات

## الاستخدام - Usage

1. **الوصول للصفحة**: انتقل إلى `/items`
2. **إضافة منتج**: انقر على "إضافة منتج جديد"
3. **البحث**: استخدم حقل البحث أو التصفية المتقدمة
4. **التعديل**: انقر على قائمة الإجراءات ← تعديل
5. **إدارة المخزون**: انقر على قائمة الإجراءات ← تحديث المخزون

## التطوير المستقبلي - Future Development

- إضافة تصدير البيانات (Excel/PDF)
- تقارير المخزون المتقدمة
- إشعارات المخزون المنخفض
- دعم الباركود سكانر
- تكامل مع أنظمة المحاسبة

## الاختبار - Testing

للاختبار:
1. تشغيل الخادم: `npm run dev`
2. فتح المتصفح: `http://localhost:5174`
3. تسجيل الدخول كوكيل
4. الانتقال إلى صفحة إدارة المنتجات

## الدعم الفني - Technical Support

- جميع النصوص باللغة العربية
- دعم RTL كامل
- تصميم متجاوب
- معالجة شاملة للأخطاء
- إشعارات المستخدم
