import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Plus, Search, Filter } from "lucide-react"

import { AppSidebar } from "~/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb"
import { Button } from "~/components/ui/button"
import { Input } from "~/components/ui/input"
import { Separator } from "~/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar"
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card"

import { getMyItems } from "~/lib/api/items"
import { ItemTable } from "./components/ItemTable"
import { ItemCreateDialog } from "./components/ItemCreateDialog"
import { useAuth } from "~/lib/auth-context"
import { Navigate } from "react-router"

export default function ItemsPage() {
  const { isLoggedIn } = useAuth()
  if (!isLoggedIn) {
    return <Navigate to="/" replace />
  }

  const [searchTerm, setSearchTerm] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // Fetch items data
  const {
    data: items = [],
    isLoading: isLoadingItems,
    error,
    refetch,
  } = useQuery({
    queryKey: ["items"],
    queryFn: getMyItems,
  })

  // Filter items based on search term
  const filteredItems = items.filter((item) =>
    item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.product.barcode.includes(searchTerm)
  )

  const handleItemCreated = () => {
    refetch()
    setIsCreateDialogOpen(false)
  }

  const handleItemUpdated = () => {
    refetch()
  }

  const handleItemDeleted = () => {
    refetch()
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    لوحة التحكم
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>إدارة المنتجات</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">إدارة المنتجات</h1>
              <p className="text-muted-foreground">
                إدارة منتجاتك وأسعارها ومخزونك
              </p>
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="ml-2 h-4 w-4" />
              إضافة منتج جديد
            </Button>
          </div>

          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                البحث والتصفية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="البحث في المنتجات (الاسم أو الباركود)..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Button variant="outline">
                  <Filter className="ml-2 h-4 w-4" />
                  تصفية متقدمة
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Items Table */}
          <Card>
            <CardHeader>
              <CardTitle>قائمة المنتجات</CardTitle>
              <p className="text-sm text-muted-foreground">
                {filteredItems.length} منتج من أصل {items.length}
              </p>
            </CardHeader>
            <CardContent>
              {error ? (
                <div className="text-center py-8">
                  <p className="text-red-500 mb-4">
                    حدث خطأ في تحميل المنتجات: {error.message}
                  </p>
                  <Button onClick={() => refetch()}>إعادة المحاولة</Button>
                </div>
              ) : (
                <ItemTable
                  items={filteredItems}
                  isLoading={isLoadingItems}
                  onItemUpdated={handleItemUpdated}
                  onItemDeleted={handleItemDeleted}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </SidebarInset>

      {/* Create Item Dialog */}
      <ItemCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onItemCreated={handleItemCreated}
      />
    </SidebarProvider>
  )
}
