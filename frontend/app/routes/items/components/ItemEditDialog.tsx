import { useState, useEffect } from "react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"
import { Edit, Package } from "lucide-react"

import { Button } from "~/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Badge } from "~/components/ui/badge"

import {
  updateItem,
  type UpdateItemRequest,
  type ItemOut
} from "~/lib/api/items"

interface ItemEditDialogProps {
  item: ItemOut
  open: boolean
  onOpenChange: (open: boolean) => void
  onItemUpdated: () => void
}

export function ItemEditDialog({ item, open, onOpenChange, onItemUpdated }: ItemEditDialogProps) {
  const [formData, setFormData] = useState({
    base_price: "",
    inventory_count: "",
    minimum_order_quantity: "",
    maximum_order_quantity: "",
    price_expiry: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize form data when item changes
  useEffect(() => {
    if (item) {
      setFormData({
        base_price: item.base_price.toString(),
        inventory_count: item.inventory_count.toString(),
        minimum_order_quantity: item.minimum_order_quantity.toString(),
        maximum_order_quantity: item.maximum_order_quantity?.toString() || "",
        price_expiry: item.price_expiry
          ? new Date(item.price_expiry).toISOString().slice(0, 16)
          : "",
      })
      setErrors({})
    }
  }, [item])

  const updateMutation = useMutation({
    mutationFn: (data: UpdateItemRequest) => updateItem(item.id, data),
    onSuccess: () => {
      toast.success("تم تحديث المنتج بنجاح")
      onItemUpdated()
      onOpenChange(false)
    },
    onError: (error) => {
      console.error("Error updating item:", error)
      toast.error(`فشل في تحديث المنتج: ${error.message}`)
      setErrors({ general: error.message })
    },
  })

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.base_price || parseFloat(formData.base_price) <= 0) {
      newErrors.base_price = "يجب إدخال سعر صحيح"
    }

    if (parseInt(formData.inventory_count) < 0) {
      newErrors.inventory_count = "لا يمكن أن يكون المخزون سالباً"
    }

    if (parseInt(formData.minimum_order_quantity) <= 0) {
      newErrors.minimum_order_quantity = "يجب أن يكون الحد الأدنى للطلب أكبر من صفر"
    }

    if (formData.maximum_order_quantity &&
      parseInt(formData.maximum_order_quantity) < parseInt(formData.minimum_order_quantity)) {
      newErrors.maximum_order_quantity = "الحد الأقصى يجب أن يكون أكبر من الحد الأدنى"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const requestData: UpdateItemRequest = {
      base_price: parseFloat(formData.base_price),
      inventory_count: parseInt(formData.inventory_count),
      minimum_order_quantity: parseInt(formData.minimum_order_quantity),
      maximum_order_quantity: formData.maximum_order_quantity
        ? parseInt(formData.maximum_order_quantity)
        : null,
      price_expiry: formData.price_expiry || null,
    }

    updateMutation.mutate(requestData)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            تعديل المنتج
          </DialogTitle>
          <DialogDescription>
            تعديل بيانات المنتج والسعر والمخزون
          </DialogDescription>
        </DialogHeader>

        {/* Product Info (Read-only) */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            {item.product.image_url ? (
              <img
                src={item.product.image_url}
                alt={item.product.name}
                className="h-12 w-12 rounded object-cover"
              />
            ) : (
              <div className="h-12 w-12 rounded bg-muted flex items-center justify-center">
                <Package className="h-6 w-6 text-muted-foreground" />
              </div>
            )}
            <div className="flex-1">
              <h3 className="font-medium">{item.product.name}</h3>
              <p className="text-sm text-muted-foreground">
                الباركود: {item.product.barcode}
              </p>
              <p className="text-sm text-muted-foreground">
                تاريخ الإضافة: {formatDate(item.created_at)}
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Price */}
          <div className="space-y-2">
            <Label htmlFor="base_price">السعر (ج.م) *</Label>
            <Input
              id="base_price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.base_price}
              onChange={(e) => handleInputChange("base_price", e.target.value)}
              className={errors.base_price ? "border-red-500" : ""}
            />
            {errors.base_price && (
              <p className="text-sm text-red-500">{errors.base_price}</p>
            )}
          </div>

          {/* Inventory Count */}
          <div className="space-y-2">
            <Label htmlFor="inventory_count">الكمية المتاحة</Label>
            <Input
              id="inventory_count"
              type="number"
              min="0"
              placeholder="0"
              value={formData.inventory_count}
              onChange={(e) => handleInputChange("inventory_count", e.target.value)}
              className={errors.inventory_count ? "border-red-500" : ""}
            />
            {errors.inventory_count && (
              <p className="text-sm text-red-500">{errors.inventory_count}</p>
            )}
          </div>

          {/* Order Quantities */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minimum_order_quantity">الحد الأدنى للطلب *</Label>
              <Input
                id="minimum_order_quantity"
                type="number"
                min="1"
                placeholder="1"
                value={formData.minimum_order_quantity}
                onChange={(e) => handleInputChange("minimum_order_quantity", e.target.value)}
                className={errors.minimum_order_quantity ? "border-red-500" : ""}
              />
              {errors.minimum_order_quantity && (
                <p className="text-sm text-red-500">{errors.minimum_order_quantity}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="maximum_order_quantity">الحد الأقصى للطلب</Label>
              <Input
                id="maximum_order_quantity"
                type="number"
                min="1"
                placeholder="اختياري"
                value={formData.maximum_order_quantity}
                onChange={(e) => handleInputChange("maximum_order_quantity", e.target.value)}
                className={errors.maximum_order_quantity ? "border-red-500" : ""}
              />
              {errors.maximum_order_quantity && (
                <p className="text-sm text-red-500">{errors.maximum_order_quantity}</p>
              )}
            </div>
          </div>

          {/* Price Expiry */}
          <div className="space-y-2">
            <Label htmlFor="price_expiry">تاريخ انتهاء السعر (اختياري)</Label>
            <Input
              id="price_expiry"
              type="datetime-local"
              value={formData.price_expiry}
              onChange={(e) => handleInputChange("price_expiry", e.target.value)}
            />
          </div>

          {/* General Error */}
          {errors.general && (
            <div className="text-sm text-red-500 bg-red-50 p-3 rounded">
              {errors.general}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={updateMutation.isPending}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={updateMutation.isPending}>
              {updateMutation.isPending ? "جاري الحفظ..." : "حفظ التغييرات"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
