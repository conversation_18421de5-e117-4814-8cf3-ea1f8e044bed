import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Archive
} from "lucide-react"

import { Button } from "~/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table"
import { Badge } from "~/components/ui/badge"
import { Skeleton } from "~/components/ui/skeleton"

import { deleteItem, type ItemOut } from "~/lib/api/items"
import { ItemEditDialog } from "./ItemEditDialog"
import { InventoryUpdateDialog } from "./InventoryUpdateDialog"

interface ItemTableProps {
  items: ItemOut[]
  isLoading: boolean
  onItemUpdated: () => void
  onItemDeleted: () => void
}

export function ItemTable({ items, isLoading, onItemUpdated, onItemDeleted }: ItemTableProps) {
  const [editingItem, setEditingItem] = useState<ItemOut | null>(null)
  const [inventoryItem, setInventoryItem] = useState<ItemOut | null>(null)

  const deleteMutation = useMutation({
    mutationFn: deleteItem,
    onSuccess: () => {
      toast.success("تم حذف المنتج بنجاح")
      onItemDeleted()
    },
    onError: (error) => {
      console.error("Error deleting item:", error)
      toast.error(`فشل في حذف المنتج: ${error.message}`)
    },
  })

  const handleDelete = (item: ItemOut) => {
    if (confirm(`هل أنت متأكد من حذف المنتج "${item.product.name}"؟`)) {
      deleteMutation.mutate(item.id)
    }
  }

  const getStockStatus = (inventoryCount: number) => {
    if (inventoryCount === 0) {
      return { label: "نفد المخزون", variant: "destructive" as const, icon: XCircle }
    } else if (inventoryCount < 10) {
      return { label: "مخزون منخفض", variant: "secondary" as const, icon: AlertTriangle }
    } else {
      return { label: "متوفر", variant: "default" as const, icon: CheckCircle }
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("ar-EG", {
      style: "currency",
      currency: "EGP",
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("ar-EG", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (items.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">لا توجد منتجات</h3>
        <p className="text-muted-foreground">
          لم يتم العثور على أي منتجات. ابدأ بإضافة منتج جديد.
        </p>
      </div>
    )
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>المنتج</TableHead>
              <TableHead>الباركود</TableHead>
              <TableHead>السعر</TableHead>
              <TableHead>المخزون</TableHead>
              <TableHead>الحد الأدنى للطلب</TableHead>
              <TableHead>الحد الأقصى للطلب</TableHead>
              <TableHead>تاريخ الإضافة</TableHead>
              <TableHead className="w-[70px]">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => {
              const stockStatus = getStockStatus(item.inventory_count)
              const StockIcon = stockStatus.icon

              return (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      {item.product.image_url ? (
                        <img
                          src={item.product.image_url}
                          alt={item.product.name}
                          className="h-10 w-10 rounded object-cover"
                        />
                      ) : (
                        <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
                          <Package className="h-5 w-5 text-muted-foreground" />
                        </div>
                      )}
                      <div>
                        <div className="font-medium">{item.product.name}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {item.product.barcode}
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatPrice(item.base_price)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge variant={stockStatus.variant} className="flex items-center gap-1">
                        <StockIcon className="h-3 w-3" />
                        {item.inventory_count}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{item.minimum_order_quantity}</TableCell>
                  <TableCell>
                    {item.maximum_order_quantity || "غير محدد"}
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {formatDate(item.created_at)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">فتح القائمة</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>الإجراءات</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => setEditingItem(item)}>
                          <Edit className="ml-2 h-4 w-4" />
                          تعديل
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setInventoryItem(item)}>
                          <Archive className="ml-2 h-4 w-4" />
                          تحديث المخزون
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDelete(item)}
                          className="text-red-600"
                          disabled={deleteMutation.isPending}
                        >
                          <Trash2 className="ml-2 h-4 w-4" />
                          {deleteMutation.isPending ? "جاري الحذف..." : "حذف"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* Edit Dialog */}
      {editingItem && (
        <ItemEditDialog
          item={editingItem}
          open={!!editingItem}
          onOpenChange={(open) => !open && setEditingItem(null)}
          onItemUpdated={() => {
            onItemUpdated()
            setEditingItem(null)
          }}
        />
      )}

      {/* Inventory Update Dialog */}
      {inventoryItem && (
        <InventoryUpdateDialog
          item={inventoryItem}
          open={!!inventoryItem}
          onOpenChange={(open) => !open && setInventoryItem(null)}
          onInventoryUpdated={() => {
            onItemUpdated()
            setInventoryItem(null)
          }}
        />
      )}
    </>
  )
}
