import { useState, useEffect, useMemo } from "react"
import { useQuery } from "@tanstack/react-query"
import { Search, Package, Building2, ChevronDown } from "lucide-react"

import { Button } from "~/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "~/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover"
import { Label } from "~/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select"
import { Badge } from "~/components/ui/badge"

import {
  getCategoriesAndCompanies,
  getProducts,
  type Product,
  type Company,
  type ProductsResponse
} from "~/lib/api/items"

interface ProductSearchFieldProps {
  selectedProduct: Product | null
  onProductSelect: (product: Product | null) => void
  error?: string
}

export function ProductSearchField({ selectedProduct, onProductSelect, error }: ProductSearchFieldProps) {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCompany, setSelectedCompany] = useState<number | null>(null)

  // Fetch categories and companies for filtering
  const { data: categoriesCompanies } = useQuery({
    queryKey: ["categories-companies"],
    queryFn: getCategoriesAndCompanies,
  })

  // Fetch products with search and company filter
  const { data: productsData, isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products", searchTerm, selectedCompany],
    queryFn: async () => {
      const result = await getProducts({
        search: searchTerm || undefined,
        company_id: selectedCompany || undefined,
        size: 20,
      })
      return result
    },
    enabled: open, // Only fetch when dropdown is open
  })

  const companies = categoriesCompanies?.companies || []


  const handleProductSelect = (product: Product) => {
    onProductSelect(product)
    setOpen(false)
  }

  const handleCompanyChange = (companyId: string) => {
    const id = companyId === "all" ? null : parseInt(companyId)
    setSelectedCompany(id)
  }

  const getDisplayText = () => {
    if (selectedProduct) {
      return (
        <div className="flex items-center gap-2">
          {selectedProduct.image_url ? (
            <img
              src={selectedProduct.image_url}
              alt={selectedProduct.name}
              className="h-6 w-6 rounded object-cover"
            />
          ) : (
            <Package className="h-4 w-4 text-muted-foreground" />
          )}
          <span className="truncate">{selectedProduct.name}</span>
          {selectedProduct.company && (
            <Badge variant="secondary" className="text-xs">
              {selectedProduct.company.name}
            </Badge>
          )}
        </div>
      )
    }
    return "اختر منتجاً..."
  }

  return (
    <div className="space-y-2">
      {/* Company Filter */}
      <div className="space-y-1">
        <Label className="text-sm text-muted-foreground">تصفية حسب الشركة (اختياري)</Label>
        <Select value={selectedCompany?.toString() || "all"} onValueChange={handleCompanyChange}>
          <SelectTrigger>
            <SelectValue placeholder="جميع الشركات" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">جميع الشركات</SelectItem>
            {companies.map((company) => (
              <SelectItem key={company.id} value={company.id.toString()}>
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  {company.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Product Search */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={`w-full justify-between ${error ? "border-red-500" : ""}`}
          >
            {getDisplayText()}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[400px] p-0">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="البحث في المنتجات..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            <CommandList>
              {isLoadingProducts ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  جاري البحث...
                </div>
              ) : (productsData?.items || []).length === 0 ? (
                <CommandEmpty>
                  لم يتم العثور على منتجات
                </CommandEmpty>
              ) : (
                <CommandGroup >
                  <div className="text-xs p-2 text-muted-foreground">
                    عرض {productsData?.items.length} منتج
                  </div>
                  {productsData?.items.map((product: Product) => (
                    <CommandItem
                      key={product.id}
                      value={product.name}
                      onSelect={() => handleProductSelect(product)}
                      className="flex items-center gap-3 p-3"
                    >
                      {product.image_url ? (
                        <img
                          src={product.image_url}
                          alt={product.name}
                          className="h-8 w-8 rounded object-cover"
                        />
                      ) : (
                        <div className="h-8 w-8 rounded bg-muted flex items-center justify-center">
                          <Package className="h-4 w-4 text-muted-foreground" />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{product.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {product.barcode}
                        </div>
                        {product.company && (
                          <Badge variant="outline" className="text-xs mt-1">
                            {product.company.name}
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {product.unit_count} {product.unit}
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedProduct && (
        <div className="text-sm text-muted-foreground">
          الباركود: {selectedProduct.barcode} |
          الوحدة: {selectedProduct.unit_count} {selectedProduct.unit}
          {selectedProduct.company && ` | الشركة: ${selectedProduct.company.name}`}
        </div>
      )}
    </div>
  )
}
