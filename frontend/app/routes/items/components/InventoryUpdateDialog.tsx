import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"
import { Package, Plus, Minus, RotateCcw } from "lucide-react"

import { Button } from "~/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { Textarea } from "~/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select"
import { Badge } from "~/components/ui/badge"

import {
  updateInventory,
  type ItemOut
} from "~/lib/api/items"

interface InventoryUpdateDialogProps {
  item: ItemOut
  open: boolean
  onOpenChange: (open: boolean) => void
  onInventoryUpdated: () => void
}

export function InventoryUpdateDialog({
  item,
  open,
  onOpenChange,
  onInventoryUpdated
}: InventoryUpdateDialogProps) {
  const [transactionType, setTransactionType] = useState<"ADDITION" | "SUBTRACTION">("ADDITION")
  const [quantity, setQuantity] = useState("")
  const [notes, setNotes] = useState("")
  const [errors, setErrors] = useState<Record<string, string>>({})

  const updateMutation = useMutation({
    mutationFn: ({ itemId, data }: { itemId: number; data: { quantity: number; transaction_type: "ADDITION" | "SUBTRACTION"; notes?: string } }) =>
      updateInventory(itemId, data),
    onSuccess: () => {
      const action = transactionType === "ADDITION" ? "إضافة" : "خصم"
      toast.success(`تم ${action} ${quantity} قطعة ${transactionType === "ADDITION" ? "إلى" : "من"} المخزون`)
      onInventoryUpdated()
      onOpenChange(false)
      // Reset form
      setQuantity("")
      setNotes("")
      setErrors({})
    },
    onError: (error) => {
      console.error("Error updating inventory:", error)
      toast.error(`فشل في تحديث المخزون: ${error.message}`)
      setErrors({ general: error.message })
    },
  })

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!quantity || parseInt(quantity) <= 0) {
      newErrors.quantity = "يجب إدخال كمية صحيحة"
    }

    if (transactionType === "SUBTRACTION" && parseInt(quantity) > item.inventory_count) {
      newErrors.quantity = `لا يمكن خصم أكثر من ${item.inventory_count} قطعة`
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    updateMutation.mutate({
      itemId: item.id,
      data: {
        quantity: parseInt(quantity),
        transaction_type: transactionType,
        notes: notes || undefined,
      }
    })
  }

  const handleInputChange = (field: string, value: string) => {
    if (field === "quantity") {
      setQuantity(value)
    } else if (field === "notes") {
      setNotes(value)
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const getNewInventoryCount = () => {
    if (!quantity) return item.inventory_count
    const qty = parseInt(quantity)
    return transactionType === "ADDITION"
      ? item.inventory_count + qty
      : item.inventory_count - qty
  }

  const getStockStatus = (count: number) => {
    if (count === 0) {
      return { label: "نفد المخزون", variant: "destructive" as const }
    } else if (count < 10) {
      return { label: "مخزون منخفض", variant: "secondary" as const }
    } else {
      return { label: "متوفر", variant: "default" as const }
    }
  }

  const currentStatus = getStockStatus(item.inventory_count)
  const newStatus = getStockStatus(getNewInventoryCount())

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            تحديث المخزون
          </DialogTitle>
          <DialogDescription>
            إضافة أو خصم كمية من مخزون المنتج
          </DialogDescription>
        </DialogHeader>

        {/* Product Info */}
        <div className="bg-muted/50 p-4 rounded-lg">
          <div className="flex items-center gap-3">
            {item.product.image_url ? (
              <img
                src={item.product.image_url}
                alt={item.product.name}
                className="h-10 w-10 rounded object-cover"
              />
            ) : (
              <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
                <Package className="h-5 w-5 text-muted-foreground" />
              </div>
            )}
            <div className="flex-1">
              <h3 className="font-medium">{item.product.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-muted-foreground">المخزون الحالي:</span>
                <Badge variant={currentStatus.variant}>
                  {item.inventory_count} قطعة
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Transaction Type */}
          <div className="space-y-2">
            <Label>نوع العملية</Label>
            <Select value={transactionType} onValueChange={(value: "ADDITION" | "SUBTRACTION") => setTransactionType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ADDITION">
                  <div className="flex items-center gap-2">
                    <Plus className="h-4 w-4 text-green-600" />
                    إضافة إلى المخزون
                  </div>
                </SelectItem>
                <SelectItem value="SUBTRACTION">
                  <div className="flex items-center gap-2">
                    <Minus className="h-4 w-4 text-red-600" />
                    خصم من المخزون
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity">الكمية *</Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              max={transactionType === "SUBTRACTION" ? item.inventory_count : undefined}
              placeholder="أدخل الكمية"
              value={quantity}
              onChange={(e) => handleInputChange("quantity", e.target.value)}
              className={errors.quantity ? "border-red-500" : ""}
            />
            {errors.quantity && (
              <p className="text-sm text-red-500">{errors.quantity}</p>
            )}
          </div>

          {/* Preview */}
          {quantity && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">المخزون بعد التحديث:</span>
                <div className="flex items-center gap-2">
                  <Badge variant={currentStatus.variant}>
                    {item.inventory_count}
                  </Badge>
                  <RotateCcw className="h-4 w-4 text-muted-foreground" />
                  <Badge variant={newStatus.variant}>
                    {getNewInventoryCount()}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">ملاحظات (اختياري)</Label>
            <Textarea
              id="notes"
              placeholder="أضف ملاحظة حول سبب التحديث..."
              value={notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={3}
            />
          </div>

          {/* General Error */}
          {errors.general && (
            <div className="text-sm text-red-500 bg-red-50 p-3 rounded">
              {errors.general}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={updateMutation.isPending}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={updateMutation.isPending}
              variant={transactionType === "ADDITION" ? "default" : "destructive"}
            >
              {updateMutation.isPending ? "جاري التحديث..." :
                transactionType === "ADDITION" ? "إضافة إلى المخزون" : "خصم من المخزون"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
