import { useState, useEffect } from "react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"
import { Package } from "lucide-react"

import { Button } from "~/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"

import {
  createItem,
  type CreateItemRequest,
  type Product
} from "~/lib/api/items"
import { ProductSearchField } from "./ProductSearchField"

interface ItemCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onItemCreated: () => void
}

export function ItemCreateDialog({ open, onOpenChange, onItemCreated }: ItemCreateDialogProps) {
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [formData, setFormData] = useState({
    base_price: "",
    inventory_count: "0",
    minimum_order_quantity: "1",
    maximum_order_quantity: "",
    price_expiry: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedProduct(null)
      setFormData({
        base_price: "",
        inventory_count: "0",
        minimum_order_quantity: "1",
        maximum_order_quantity: "",
        price_expiry: "",
      })
      setErrors({})
    }
  }, [open])

  const createMutation = useMutation({
    mutationFn: createItem,
    onSuccess: () => {
      toast.success("تم إضافة المنتج بنجاح")
      onItemCreated()
      onOpenChange(false)
    },
    onError: (error) => {
      console.error("Error creating item:", error)
      toast.error(`فشل في إضافة المنتج: ${error.message}`)
      setErrors({ general: error.message })
    },
  })

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!selectedProduct) {
      newErrors.product = "يجب اختيار منتج"
    }

    if (!formData.base_price || parseFloat(formData.base_price) <= 0) {
      newErrors.base_price = "يجب إدخال سعر صحيح"
    }

    if (parseInt(formData.inventory_count) < 0) {
      newErrors.inventory_count = "لا يمكن أن يكون المخزون سالباً"
    }

    if (parseInt(formData.minimum_order_quantity) <= 0) {
      newErrors.minimum_order_quantity = "يجب أن يكون الحد الأدنى للطلب أكبر من صفر"
    }

    if (formData.maximum_order_quantity &&
      parseInt(formData.maximum_order_quantity) < parseInt(formData.minimum_order_quantity)) {
      newErrors.maximum_order_quantity = "الحد الأقصى يجب أن يكون أكبر من الحد الأدنى"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    const requestData: CreateItemRequest = {
      product_id: selectedProduct!.id,
      base_price: parseFloat(formData.base_price),
      inventory_count: parseInt(formData.inventory_count),
      minimum_order_quantity: parseInt(formData.minimum_order_quantity),
      maximum_order_quantity: formData.maximum_order_quantity
        ? parseInt(formData.maximum_order_quantity)
        : null,
      price_expiry: formData.price_expiry || null,
    }

    createMutation.mutate(requestData)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            إضافة منتج جديد
          </DialogTitle>
          <DialogDescription>
            أضف منتجاً جديداً إلى مخزونك مع تحديد السعر والكمية المتاحة
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Product Selection */}
          <div className="space-y-2">
            <Label htmlFor="product">المنتج *</Label>
            <ProductSearchField
              selectedProduct={selectedProduct}
              onProductSelect={setSelectedProduct}
              error={errors.product}
            />
            {errors.product && (
              <p className="text-sm text-red-500">{errors.product}</p>
            )}
          </div>

          {/* Price */}
          <div className="space-y-2">
            <Label htmlFor="base_price">السعر (ج.م) *</Label>
            <Input
              id="base_price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.base_price}
              onChange={(e) => handleInputChange("base_price", e.target.value)}
              className={errors.base_price ? "border-red-500" : ""}
            />
            {errors.base_price && (
              <p className="text-sm text-red-500">{errors.base_price}</p>
            )}
          </div>

          {/* Inventory Count */}
          <div className="space-y-2">
            <Label htmlFor="inventory_count">الكمية المتاحة</Label>
            <Input
              id="inventory_count"
              type="number"
              min="0"
              placeholder="0"
              value={formData.inventory_count}
              onChange={(e) => handleInputChange("inventory_count", e.target.value)}
              className={errors.inventory_count ? "border-red-500" : ""}
            />
            {errors.inventory_count && (
              <p className="text-sm text-red-500">{errors.inventory_count}</p>
            )}
          </div>

          {/* Order Quantities */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="minimum_order_quantity">الحد الأدنى للطلب *</Label>
              <Input
                id="minimum_order_quantity"
                type="number"
                min="1"
                placeholder="1"
                value={formData.minimum_order_quantity}
                onChange={(e) => handleInputChange("minimum_order_quantity", e.target.value)}
                className={errors.minimum_order_quantity ? "border-red-500" : ""}
              />
              {errors.minimum_order_quantity && (
                <p className="text-sm text-red-500">{errors.minimum_order_quantity}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="maximum_order_quantity">الحد الأقصى للطلب</Label>
              <Input
                id="maximum_order_quantity"
                type="number"
                min="1"
                placeholder="اختياري"
                value={formData.maximum_order_quantity}
                onChange={(e) => handleInputChange("maximum_order_quantity", e.target.value)}
                className={errors.maximum_order_quantity ? "border-red-500" : ""}
              />
              {errors.maximum_order_quantity && (
                <p className="text-sm text-red-500">{errors.maximum_order_quantity}</p>
              )}
            </div>
          </div>

          {/* Price Expiry */}
          <div className="space-y-2">
            <Label htmlFor="price_expiry">تاريخ انتهاء السعر (اختياري)</Label>
            <Input
              id="price_expiry"
              type="datetime-local"
              value={formData.price_expiry}
              onChange={(e) => handleInputChange("price_expiry", e.target.value)}
            />
          </div>

          {/* General Error */}
          {errors.general && (
            <div className="text-sm text-red-500 bg-red-50 p-3 rounded">
              {errors.general}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={createMutation.isPending}
            >
              إلغاء
            </Button>
            <Button type="submit" disabled={createMutation.isPending}>
              {createMutation.isPending ? "جاري الإضافة..." : "إضافة المنتج"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
