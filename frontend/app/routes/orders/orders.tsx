import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Search, Filter, Package, Printer } from "lucide-react"

import { AppSidebar } from "~/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb"
import { Button } from "~/components/ui/button"
import { Input } from "~/components/ui/input"
import { Separator } from "~/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar"
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select"

import { getWholesalerOrders, OrderStatus } from "~/lib/api/orders"
import { OrdersTable } from "./components/OrdersTable"
import { OrderDetailsDialog } from "./components/OrderDetailsDialog"
import { PrintUtility, usePrint } from "./components/PrintUtility"
import { useAuth } from "~/lib/auth-context"
import { Navigate } from "react-router"

function OrdersPageContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(0)
  const [pageSize] = useState(10)

  const { printMultipleOrders, isPrinting } = usePrint()

  // Fetch orders data
  const {
    data: ordersResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["wholesaler-orders", currentPage, pageSize],
    queryFn: () => getWholesalerOrders(currentPage * pageSize, pageSize),
  })

  const orders = ordersResponse?.orders || []

  // Filter orders based on search term and status
  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.id.toString().includes(searchTerm) ||
      order.store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.store.owner.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.store.owner.last_name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || order.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleOrderUpdated = () => {
    refetch()
  }

  const handleViewDetails = (orderId: number) => {
    setSelectedOrderId(orderId)
    setIsDetailsDialogOpen(true)
  }

  const handleCloseDetails = () => {
    setSelectedOrderId(null)
    setIsDetailsDialogOpen(false)
  }

  const handlePrintAllOrders = async () => {
    if (filteredOrders.length === 0) {
      return // The print utility will handle the error message
    }
    await printMultipleOrders(filteredOrders)
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    لوحة التحكم
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>إدارة الطلبات</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">إدارة الطلبات</h1>
              <p className="text-muted-foreground">
                إدارة طلبات العملاء ومتابعة حالة الطلبات
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {filteredOrders.length} طلب
                </span>
              </div>
              <Button
                onClick={handlePrintAllOrders}
                disabled={isPrinting || filteredOrders.length === 0}
                variant="outline"
              >
                <Printer className="h-4 w-4 ml-2" />
                {isPrinting ? "جاري الطباعة..." : "طباعة جميع الطلبات"}
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                البحث والتصفية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="البحث في الطلبات (رقم الطلب، اسم المتجر، اسم العميل)..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-48">
                      <SelectValue placeholder="تصفية حسب الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value={OrderStatus.PENDING}>في الانتظار</SelectItem>
                      <SelectItem value={OrderStatus.PROCESSING}>قيد المعالجة</SelectItem>
                      <SelectItem value={OrderStatus.SHIPPED}>تم الشحن</SelectItem>
                      <SelectItem value={OrderStatus.DELIVERED}>تم التسليم</SelectItem>
                      <SelectItem value={OrderStatus.CANCELLED}>ملغي</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" className="w-full sm:w-auto">
                    <Filter className="ml-2 h-4 w-4" />
                    تصفية متقدمة
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Orders Table */}
          <Card>
            <CardHeader>
              <CardTitle>قائمة الطلبات</CardTitle>
              <p className="text-sm text-muted-foreground">
                {filteredOrders.length} طلب من أصل {orders.length}
              </p>
            </CardHeader>
            <CardContent>
              {error ? (
                <div className="text-center py-8">
                  <p className="text-red-500 mb-4">
                    حدث خطأ في تحميل الطلبات: {error.message}
                  </p>
                  <Button onClick={() => refetch()}>إعادة المحاولة</Button>
                </div>
              ) : (
                <OrdersTable
                  orders={filteredOrders}
                  isLoading={isLoading}
                  onOrderUpdated={handleOrderUpdated}
                  onViewDetails={handleViewDetails}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </SidebarInset>

      {/* Order Details Dialog */}
      {selectedOrderId && (
        <OrderDetailsDialog
          orderId={selectedOrderId}
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
          onClose={handleCloseDetails}
        />
      )}
    </SidebarProvider>
  )
}

export default function OrdersPage() {
  const { isLoggedIn } = useAuth()
  if (!isLoggedIn) {
    return <Navigate to="/" replace />
  }
  return (
    <PrintUtility>
      <OrdersPageContent />
    </PrintUtility>
  )
}
