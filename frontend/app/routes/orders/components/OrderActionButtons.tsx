import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { Check, X, Ban, Package } from "lucide-react"
import { toast } from "sonner"

import { But<PERSON> } from "~/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"

import {
  type WholesalerOrder,
  OrderStatus,
  acceptOrder,
  rejectOrder,
  cancelOrder,
  completeOrder
} from "~/lib/api/orders"

interface OrderActionButtonsProps {
  order: WholesalerOrder
  onOrderUpdated: () => void
}

export function OrderActionButtons({ order, onOrderUpdated }: OrderActionButtonsProps) {
  const [showAcceptDialog, setShowAcceptDialog] = useState(false)
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [showCancelDialog, setShowCancelDialog] = useState(false)
  const [showCompleteDialog, setShowCompleteDialog] = useState(false)
  const [finalPrice, setFinalPrice] = useState(order.total_price.toString())

  // Accept order mutation
  const acceptMutation = useMutation({
    mutationFn: () => acceptOrder(order.id),
    onSuccess: (data) => {
      if (data.success) {
        toast.success("تم قبول الطلب بنجاح")
        onOrderUpdated()
        setShowAcceptDialog(false)
      } else {
        toast.error(data.error || "حدث خطأ في قبول الطلب")
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || "حدث خطأ في قبول الطلب")
    },
  })

  // Reject order mutation
  const rejectMutation = useMutation({
    mutationFn: () => rejectOrder(order.id),
    onSuccess: (data) => {
      if (data.success) {
        toast.success("تم رفض الطلب بنجاح")
        onOrderUpdated()
        setShowRejectDialog(false)
      } else {
        toast.error(data.error || "حدث خطأ في رفض الطلب")
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || "حدث خطأ في رفض الطلب")
    },
  })

  // Cancel order mutation
  const cancelMutation = useMutation({
    mutationFn: () => cancelOrder(order.id),
    onSuccess: (data) => {
      if (data.success) {
        toast.success("تم إلغاء الطلب بنجاح")
        onOrderUpdated()
        setShowCancelDialog(false)
      } else {
        toast.error(data.error || "حدث خطأ في إلغاء الطلب")
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || "حدث خطأ في إلغاء الطلب")
    },
  })

  // Complete order mutation
  const completeMutation = useMutation({
    mutationFn: () => completeOrder({
      order_id: order.id,
      final_completed_price: parseFloat(finalPrice) || order.total_price
    }),
    onSuccess: (data) => {
      if (data.success) {
        toast.success("تم إكمال الطلب بنجاح")
        onOrderUpdated()
        setShowCompleteDialog(false)
      } else {
        toast.error(data.error || "حدث خطأ في إكمال الطلب")
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || "حدث خطأ في إكمال الطلب")
    },
  })

  const canAccept = order.status === OrderStatus.PENDING
  const canReject = order.status === OrderStatus.PENDING || order.status === OrderStatus.PROCESSING
  const canCancel = order.status === OrderStatus.PENDING || order.status === OrderStatus.PROCESSING
  const canComplete = order.status === OrderStatus.PROCESSING

  if (!canAccept && !canReject && !canCancel && !canComplete) {
    return null
  }

  return (
    <>
      <div className="flex items-center gap-1 flex-wrap">
        {canAccept && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAcceptDialog(true)}
            className="text-green-600 border-green-200 hover:bg-green-50"
          >
            <Check className="h-3 w-3 ml-1" />
            قبول
          </Button>
        )}
        {canReject && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowRejectDialog(true)}
            className="text-red-600 border-red-200 hover:bg-red-50"
          >
            <X className="h-3 w-3 ml-1" />
            رفض
          </Button>
        )}
        {canCancel && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCancelDialog(true)}
            className="text-orange-600 border-orange-200 hover:bg-orange-50"
          >
            <Ban className="h-3 w-3 ml-1" />
            إلغاء
          </Button>
        )}
        {canComplete && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCompleteDialog(true)}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            <Package className="h-3 w-3 ml-1" />
            إكمال
          </Button>
        )}
      </div>

      {/* Accept Dialog */}
      <Dialog open={showAcceptDialog} onOpenChange={setShowAcceptDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد قبول الطلب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من قبول الطلب رقم #{order.id}؟ سيتم تغيير حالة الطلب إلى "قيد المعالجة".
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAcceptDialog(false)}>
              إلغاء
            </Button>
            <Button
              onClick={() => acceptMutation.mutate()}
              disabled={acceptMutation.isPending}
            >
              {acceptMutation.isPending ? "جاري القبول..." : "قبول الطلب"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد رفض الطلب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رفض الطلب رقم #{order.id}؟ سيتم إلغاء الطلب ولن يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              إلغاء
            </Button>
            <Button
              onClick={() => rejectMutation.mutate()}
              disabled={rejectMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {rejectMutation.isPending ? "جاري الرفض..." : "رفض الطلب"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد إلغاء الطلب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من إلغاء الطلب رقم #{order.id}؟ سيتم إلغاء الطلب ولن يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              إلغاء
            </Button>
            <Button
              onClick={() => cancelMutation.mutate()}
              disabled={cancelMutation.isPending}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {cancelMutation.isPending ? "جاري الإلغاء..." : "إلغاء الطلب"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Complete Dialog */}
      <Dialog open={showCompleteDialog} onOpenChange={setShowCompleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد إكمال الطلب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من إكمال الطلب رقم #{order.id}؟ يمكنك تعديل المبلغ النهائي إذا لزم الأمر.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="final-price" className="text-right">
                المبلغ النهائي
              </Label>
              <Input
                id="final-price"
                type="number"
                step="0.01"
                value={finalPrice}
                onChange={(e) => setFinalPrice(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCompleteDialog(false)}>
              إلغاء
            </Button>
            <Button
              onClick={() => completeMutation.mutate()}
              disabled={completeMutation.isPending}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {completeMutation.isPending ? "جاري الإكمال..." : "إكمال الطلب"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
