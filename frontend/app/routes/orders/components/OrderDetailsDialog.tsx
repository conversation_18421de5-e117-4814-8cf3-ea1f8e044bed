import { useQuery } from "@tanstack/react-query"
import { format } from "date-fns"
import { ar } from "date-fns/locale"
import { Package, User, MapPin, Calendar, DollarSign, Hash, Printer } from "lucide-react"

import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
import { Button } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import { Separator } from "~/components/ui/separator"
import { Skeleton } from "~/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card"

import {
  getOrderById,
  getOrderStatusInArabic,
  getOrderStatusColor
} from "~/lib/api/orders"
import { usePrint } from "./PrintUtility"

interface OrderDetailsDialogProps {
  orderId: number
  open: boolean
  onOpenChange: (open: boolean) => void
  onClose: () => void
}

export function OrderDetailsDialog({
  orderId,
  open,
  onOpenChange,
  onClose
}: OrderDetailsDialogProps) {
  const { printSingleOrder, isPrinting } = usePrint()

  const {
    data: order,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["order-details", orderId],
    queryFn: () => getOrderById(orderId),
    enabled: open && !!orderId,
  })

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: ar })
    } catch {
      return dateString
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const handlePrintOrder = async () => {
    if (order) {
      await printSingleOrder(order)
    }
  }

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>خطأ في تحميل تفاصيل الطلب</DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-red-500">
              حدث خطأ في تحميل تفاصيل الطلب: {error.message}
            </p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[150vw] max-w-[95vw] sm:max-w-[95vw] max-h-[95vh] overflow-y-auto p-4 sm:p-6">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              تفاصيل الطلب {orderId ? `#${orderId}` : ""}
            </DialogTitle>
            {order && (
              <Button
                onClick={handlePrintOrder}
                disabled={isPrinting}
                variant="outline"
                size="sm"
                className="no-print"
              >
                <Printer className="h-4 w-4 ml-2" />
                {isPrinting ? "جاري الطباعة..." : "طباعة الطلب"}
              </Button>
            )}
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        ) : order ? (
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  ملخص الطلب
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">رقم الطلب</p>
                    <p className="font-medium">#{order.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">الحالة</p>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {getOrderStatusInArabic(order.status)}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">تاريخ الطلب</p>
                    <p className="font-medium">{formatDate(order.created_at)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">موعد التسليم</p>
                    <p className="font-medium">
                      {order.deliver_at ? formatDate(order.deliver_at) : "غير محدد"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Store and Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  معلومات المتجر والعميل
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-2">معلومات المتجر</h4>
                    <div className="space-y-2">
                      <p><span className="text-muted-foreground">اسم المتجر:</span> {order.store.name}</p>
                      <p><span className="text-muted-foreground">الوصف:</span> {order.store.description}</p>
                      <div className="flex items-start gap-2">
                        <MapPin className="h-4 w-4 mt-1 text-muted-foreground" />
                        <div>
                          <p>{order.store.address}</p>
                          <p className="text-sm text-muted-foreground">
                            {order.store.city.name}, {order.store.state.name}, {order.store.country.name}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">معلومات العميل</h4>
                    <div className="space-y-2">
                      <p><span className="text-muted-foreground">الاسم:</span> {order.store.owner.first_name} {order.store.owner.last_name}</p>
                      <p><span className="text-muted-foreground">اسم المستخدم:</span> {order.store.owner.username}</p>
                      <p><span className="text-muted-foreground">البريد الإلكتروني:</span> {order.store.owner.email}</p>
                      <p><span className="text-muted-foreground">رقم الهاتف:</span> {order.store.owner.phone}</p>
                      <p><span className="text-muted-foreground">حالة التحقق:</span>
                        <Badge variant={order.store.owner.phone_verified ? "default" : "secondary"} className="mr-2">
                          {order.store.owner.phone_verified ? "محقق" : "غير محقق"}
                        </Badge>
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  عناصر الطلب ({order.order_items.length} منتج)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right min-w-[200px]">المنتج</TableHead>
                        <TableHead className="text-right min-w-[120px]">الباركود</TableHead>
                        <TableHead className="text-right min-w-[100px]">الوحدة</TableHead>
                        <TableHead className="text-right min-w-[80px]">الكمية</TableHead>
                        <TableHead className="text-right min-w-[120px]">سعر الوحدة</TableHead>
                        <TableHead className="text-right min-w-[120px]">المجموع</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {order.order_items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              {item.product.image_url && (
                                <img
                                  src={item.product.image_url}
                                  alt={item.product.name}
                                  className="h-10 w-10 rounded object-cover"
                                />
                              )}
                              <div>
                                <p className="font-medium">{item.product.title}</p>
                                <p className="text-sm text-muted-foreground">{item.product.name}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{item.product.barcode}</TableCell>
                          <TableCell>{item.product.unit} ({item.product.unit_count})</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell>{formatCurrency(item.price_per_unit)}</TableCell>
                          <TableCell className="font-medium">{formatCurrency(item.total_price)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            {/* Order Totals */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  إجمالي الطلب
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>إجمالي المنتجات:</span>
                    <span>{formatCurrency(order.products_total_price)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>الرسوم:</span>
                    <span>{formatCurrency(order.fees)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-medium text-lg">
                    <span>المجموع الكلي:</span>
                    <span>{formatCurrency(order.total_price)}</span>
                  </div>
                  {order.final_completed_price && (
                    <>
                      <div className="flex justify-between text-green-600 font-medium">
                        <span>المبلغ النهائي المدفوع:</span>
                        <span>{formatCurrency(order.final_completed_price)}</span>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Order Status History */}
            {order.status_reason && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    تاريخ الحالة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p><span className="text-muted-foreground">آخر تحديث:</span> {formatDate(order.status_updated_at)}</p>
                    <p><span className="text-muted-foreground">بواسطة:</span> {order.status_updated_by.first_name} {order.status_updated_by.last_name}</p>
                    {order.status_reason && (
                      <p><span className="text-muted-foreground">السبب:</span> {order.status_reason}</p>
                    )}
                    {order.completed_at && (
                      <p><span className="text-muted-foreground">تاريخ الإكمال:</span> {formatDate(order.completed_at)}</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  )
}
