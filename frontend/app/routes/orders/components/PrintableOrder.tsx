import { format } from "date-fns"
import { ar } from "date-fns/locale"
import { type DetailedOrder } from "~/lib/api/orders"

interface PrintableOrderProps {
  order: DetailedOrder
  showPageBreak?: boolean
}

export function PrintableOrder({ order, showPageBreak = true }: PrintableOrderProps) {
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: ar })
    } catch {
      return dateString
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const getOrderStatusInArabic = (status: string): string => {
    switch (status) {
      case "pending":
        return "في الانتظار"
      case "processing":
        return "قيد المعالجة"
      case "shipped":
        return "تم الشحن"
      case "delivered":
        return "تم التسليم"
      case "cancelled":
        return "ملغي"
      default:
        return status
    }
  }

  return (
    <div className={`print-order ${showPageBreak ? 'page-break-after' : ''}`}>
      {/* Header */}
      <div className="print-header">
        <div className="company-info">
          <h1 className="company-name">نظام إدارة الطلبات</h1>
          <p className="print-date">تاريخ الطباعة: {formatDate(new Date().toISOString())}</p>
        </div>
      </div>

      {/* Order Header */}
      <div className="order-header">
        <h2 className="order-title">تفاصيل الطلب رقم #{order.id}</h2>
        <div className="order-status">
          <span className="status-label">الحالة: </span>
          <span className="status-value">{getOrderStatusInArabic(order.status)}</span>
        </div>
      </div>

      {/* Order Summary */}
      <div className="section">
        <h3 className="section-title">ملخص الطلب</h3>
        <div className="summary-grid">
          <div className="summary-item">
            <span className="label">رقم الطلب:</span>
            <span className="value">#{order.id}</span>
          </div>
          <div className="summary-item">
            <span className="label">تاريخ الطلب:</span>
            <span className="value">{formatDate(order.created_at)}</span>
          </div>
          <div className="summary-item">
            <span className="label">موعد التسليم:</span>
            <span className="value">
              {order.deliver_at ? formatDate(order.deliver_at) : "غير محدد"}
            </span>
          </div>
          <div className="summary-item">
            <span className="label">آخر تحديث:</span>
            <span className="value">{formatDate(order.status_updated_at)}</span>
          </div>
        </div>
      </div>

      {/* Store Information */}
      <div className="section">
        <h3 className="section-title">معلومات المتجر</h3>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">اسم المتجر:</span>
            <span className="value">{order.store.name}</span>
          </div>
          <div className="info-item">
            <span className="label">الوصف:</span>
            <span className="value">{order.store.description}</span>
          </div>
          <div className="info-item">
            <span className="label">العنوان:</span>
            <span className="value">{order.store.address}</span>
          </div>
          <div className="info-item">
            <span className="label">المدينة:</span>
            <span className="value">
              {order.store.city.name}, {order.store.state.name}, {order.store.country.name}
            </span>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="section">
        <h3 className="section-title">معلومات العميل</h3>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">الاسم:</span>
            <span className="value">
              {order.store.owner.first_name} {order.store.owner.last_name}
            </span>
          </div>
          <div className="info-item">
            <span className="label">اسم المستخدم:</span>
            <span className="value">{order.store.owner.username}</span>
          </div>
          <div className="info-item">
            <span className="label">البريد الإلكتروني:</span>
            <span className="value">{order.store.owner.email}</span>
          </div>
          <div className="info-item">
            <span className="label">رقم الهاتف:</span>
            <span className="value">{order.store.owner.phone}</span>
          </div>
        </div>
      </div>

      {/* Order Items */}
      <div className="section">
        <h3 className="section-title">عناصر الطلب ({order.order_items.length} منتج)</h3>
        <table className="items-table">
          <thead>
            <tr>
              <th>المنتج</th>
              <th>الباركود</th>
              <th>الوحدة</th>
              <th>الكمية</th>
              <th>سعر الوحدة</th>
              <th>المجموع</th>
            </tr>
          </thead>
          <tbody>
            {order.order_items.map((item) => (
              <tr key={item.id}>
                <td>
                  <div className="product-info">
                    <div className="product-title">{item.product.title}</div>
                    <div className="product-name">{item.product.name}</div>
                  </div>
                </td>
                <td>{item.product.barcode}</td>
                <td>{item.product.unit} ({item.product.unit_count})</td>
                <td>{item.quantity}</td>
                <td>{formatCurrency(item.price_per_unit)}</td>
                <td className="total-cell">{formatCurrency(item.total_price)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Order Totals */}
      <div className="section">
        <h3 className="section-title">إجمالي الطلب</h3>
        <div className="totals-section">
          <div className="total-row">
            <span className="total-label">إجمالي المنتجات:</span>
            <span className="total-value">{formatCurrency(order.products_total_price)}</span>
          </div>
          <div className="total-row">
            <span className="total-label">الرسوم:</span>
            <span className="total-value">{formatCurrency(order.fees)}</span>
          </div>
          <div className="total-row grand-total">
            <span className="total-label">المجموع الكلي:</span>
            <span className="total-value">{formatCurrency(order.total_price)}</span>
          </div>
          {order.final_completed_price && (
            <div className="total-row final-price">
              <span className="total-label">المبلغ النهائي المدفوع:</span>
              <span className="total-value">{formatCurrency(order.final_completed_price)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Status History */}
      {(order.status_reason || order.completed_at) && (
        <div className="section">
          <h3 className="section-title">تاريخ الحالة</h3>
          <div className="status-history">
            <div className="status-item">
              <span className="label">آخر تحديث:</span>
              <span className="value">{formatDate(order.status_updated_at)}</span>
            </div>
            <div className="status-item">
              <span className="label">بواسطة:</span>
              <span className="value">
                {order.status_updated_by.first_name} {order.status_updated_by.last_name}
              </span>
            </div>
            {order.status_reason && (
              <div className="status-item">
                <span className="label">السبب:</span>
                <span className="value">{order.status_reason}</span>
              </div>
            )}
            {order.completed_at && (
              <div className="status-item">
                <span className="label">تاريخ الإكمال:</span>
                <span className="value">{formatDate(order.completed_at)}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="print-footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الطلبات</p>
      </div>
    </div>
  )
}
