import { useState } from "react"
import { Eye, Check, X, Ban, Package } from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import { ar } from "date-fns/locale"

import { But<PERSON> } from "~/components/ui/button"
import { Badge } from "~/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table"
import { Skeleton } from "~/components/ui/skeleton"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu"

import {
  type WholesalerOrder,
  OrderStatus,
  getOrderStatusInArabic,
  getOrderStatusColor
} from "~/lib/api/orders"
import { OrderActionButtons } from "./OrderActionButtons"

interface OrdersTableProps {
  orders: WholesalerOrder[]
  isLoading: boolean
  onOrderUpdated: () => void
  onViewDetails: (orderId: number) => void
}

export function OrdersTable({
  orders,
  isLoading,
  onOrderUpdated,
  onViewDetails
}: OrdersTableProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد طلبات</h3>
        <p className="mt-1 text-sm text-gray-500">
          لم يتم العثور على أي طلبات تطابق معايير البحث.
        </p>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: ar })
    } catch {
      return dateString
    }
  }

  const formatRelativeTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: ar
      })
    } catch {
      return dateString
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  return (
    <div className="w-full">
      {/* Mobile Card Layout */}
      <div className="block sm:hidden space-y-4">
        {orders.map((order) => (
          <div key={order.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="font-medium">#{order.id}</div>
              <Badge className={getOrderStatusColor(order.status)}>
                {getOrderStatusInArabic(order.status)}
              </Badge>
            </div>

            <div className="space-y-2">
              <div>
                <span className="text-sm text-muted-foreground">المتجر: </span>
                <span className="font-medium">{order.store.name}</span>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">العميل: </span>
                <span>{order.store.owner.first_name} {order.store.owner.last_name}</span>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">المبلغ: </span>
                <span className="font-medium">{formatCurrency(order.total_price)}</span>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">منذ: </span>
                <span>{formatRelativeTime(order.status_updated_at)}</span>
              </div>
            </div>

            <div className="flex items-center gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(order.id)}
              >
                <Eye className="h-4 w-4 ml-1" />
                عرض
              </Button>
              <OrderActionButtons
                order={order}
                onOrderUpdated={onOrderUpdated}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table Layout */}
      <div className="hidden sm:block rounded-md border overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="text-right min-w-[100px]">رقم الطلب</TableHead>
              <TableHead className="text-right min-w-[150px] hidden sm:table-cell">معلومات المتجر</TableHead>
              <TableHead className="text-right min-w-[120px]">العميل</TableHead>
              <TableHead className="text-right min-w-[140px] hidden md:table-cell">تاريخ الطلب</TableHead>
              <TableHead className="text-right min-w-[140px] hidden lg:table-cell">موعد التسليم</TableHead>
              <TableHead className="text-right min-w-[100px]">الحالة</TableHead>
              <TableHead className="text-right min-w-[120px] hidden sm:table-cell">المبلغ الإجمالي</TableHead>
              <TableHead className="text-right min-w-[100px] hidden md:table-cell">عدد المنتجات</TableHead>
              <TableHead className="text-right min-w-[200px]">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell className="font-medium">
                  <div>
                    <div>#{order.id}</div>
                    <div className="sm:hidden text-xs text-muted-foreground mt-1">
                      {order.store.name}
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden sm:table-cell">
                  <div>
                    <div className="font-medium">{order.store.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {order.store.city.name}, {order.store.state.name}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {order.store.owner.first_name} {order.store.owner.last_name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {order.store.owner.phone}
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <div className="text-sm">
                    <div>{formatRelativeTime(order.status_updated_at)}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(order.status_updated_at)}
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  {order.deliver_at ? (
                    <div className="text-sm">
                      <div>{formatRelativeTime(order.deliver_at)}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatDate(order.deliver_at)}
                      </div>
                    </div>
                  ) : (
                    "غير محدد"
                  )}
                </TableCell>
                <TableCell>
                  <div>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {getOrderStatusInArabic(order.status)}
                    </Badge>
                    <div className="md:hidden text-xs text-muted-foreground mt-1">
                      {formatRelativeTime(order.status_updated_at)}
                    </div>
                  </div>
                </TableCell>
                <TableCell className="font-medium hidden sm:table-cell">
                  {formatCurrency(order.total_price)}
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  {order.products_total_quantity} منتج
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewDetails(order.id)}
                    >
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <OrderActionButtons
                      order={order}
                      onOrderUpdated={onOrderUpdated}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
