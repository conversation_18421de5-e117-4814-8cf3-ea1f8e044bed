import { useState } from "react"
import { createPortal } from "react-dom"
import { toast } from "sonner"
import { type DetailedOrder, type WholesalerOrder, getOrderById } from "~/lib/api/orders"
import { PrintableOrder } from "./PrintableOrder"

interface PrintUtilityProps {
  children: React.ReactNode
}

export function PrintUtility({ children }: PrintUtilityProps) {
  const [isPrinting, setIsPrinting] = useState(false)
  const [printContent, setPrintContent] = useState<React.ReactNode | null>(null)

  const printSingleOrder = async (order: DetailedOrder) => {
    setIsPrinting(true)
    toast.info("جاري تحضير الطلب للطباعة...")
    try {
      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        throw new Error('فشل في فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.')
      }

      const printDocument = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>طلب رقم ${order.id}</title>
          <style>
            ${getPrintStyles()}
          </style>
        </head>
        <body>
          ${generateOrderHTML(order)}
        </body>
        </html>
      `

      printWindow.document.write(printDocument)
      printWindow.document.close()

      // Wait for content to load then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print()
          toast.success("تم تحضير الطلب للطباعة بنجاح")
          // Close window after print dialog is closed or cancelled
          const checkWindowClosed = setInterval(() => {
            if (printWindow.closed) {
              clearInterval(checkWindowClosed)
              setIsPrinting(false)
            }
          }, 1000)

          // Fallback in case the window doesn't close
          setTimeout(() => {
            if (!printWindow.closed) {
              printWindow.close()
              setIsPrinting(false)
            }
          }, 60000) // 1 minute timeout
        }, 500)
      }
    } catch (error) {
      console.error('Print error:', error)
      toast.error(`حدث خطأ أثناء الطباعة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      setIsPrinting(false)
    }
  }

  const printMultipleOrders = async (orders: WholesalerOrder[]) => {
    setIsPrinting(true)
    toast.info(`جاري تحضير ${orders.length} طلب للطباعة...`)

    try {
      // Fetch detailed information for all orders
      const detailedOrders: DetailedOrder[] = []
      let failedCount = 0

      for (let i = 0; i < orders.length; i++) {
        const order = orders[i]
        try {
          toast.info(`جاري تحميل الطلب ${i + 1} من ${orders.length}...`)
          const detailedOrder = await getOrderById(order.id)
          detailedOrders.push(detailedOrder)
        } catch (error) {
          console.error(`Failed to fetch order ${order.id}:`, error)
          failedCount++
        }
      }

      if (detailedOrders.length === 0) {
        toast.error('لا توجد طلبات متاحة للطباعة')
        return
      }

      if (failedCount > 0) {
        toast.warning(`تم تحميل ${detailedOrders.length} طلب بنجاح، فشل في تحميل ${failedCount} طلب`)
      }

      const printWindow = window.open('', '_blank')
      if (!printWindow) {
        throw new Error('فشل في فتح نافذة الطباعة. يرجى التأكد من عدم حظر النوافذ المنبثقة.')
      }

      const printDocument = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>طباعة الطلبات (${detailedOrders.length} طلب)</title>
          <style>
            ${getPrintStyles()}
          </style>
        </head>
        <body>
          ${detailedOrders.map((order, index) =>
        generateOrderHTML(order, index < detailedOrders.length - 1)
      ).join('')}
        </body>
        </html>
      `

      printWindow.document.write(printDocument)
      printWindow.document.close()

      // Wait for content to load then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print()
          toast.success(`تم تحضير ${detailedOrders.length} طلب للطباعة بنجاح`)

          // Close window after print dialog is closed or cancelled
          const checkWindowClosed = setInterval(() => {
            if (printWindow.closed) {
              clearInterval(checkWindowClosed)
              setIsPrinting(false)
            }
          }, 1000)

          // Fallback in case the window doesn't close
          setTimeout(() => {
            if (!printWindow.closed) {
              printWindow.close()
              setIsPrinting(false)
            }
          }, 60000) // 1 minute timeout
        }, 1000)
      }
    } catch (error) {
      console.error('Print error:', error)
      toast.error(`حدث خطأ أثناء الطباعة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      setIsPrinting(false)
    }
  }

  const generateOrderHTML = (order: DetailedOrder, showPageBreak: boolean = false): string => {
    const formatDate = (dateString: string) => {
      try {
        return new Date(dateString).toLocaleDateString('ar-EG', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch {
        return dateString
      }
    }

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2,
      }).format(amount)
    }

    const getOrderStatusInArabic = (status: string): string => {
      switch (status) {
        case "pending": return "في الانتظار"
        case "processing": return "قيد المعالجة"
        case "shipped": return "تم الشحن"
        case "delivered": return "تم التسليم"
        case "cancelled": return "ملغي"
        default: return status
      }
    }

    return `
      <div class="print-order ${showPageBreak ? 'page-break-after' : ''}">
        <!-- Header -->
        <div class="print-header">
          <div class="company-info">
            <h1 class="company-name">نظام إدارة الطلبات</h1>
            <p class="print-date">تاريخ الطباعة: ${formatDate(new Date().toISOString())}</p>
          </div>
        </div>

        <!-- Order Header -->
        <div class="order-header">
          <h2 class="order-title">تفاصيل الطلب رقم #${order.id}</h2>
          <div class="order-status">
            <span class="status-label">الحالة: </span>
            <span class="status-value">${getOrderStatusInArabic(order.status)}</span>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="section">
          <h3 class="section-title">ملخص الطلب</h3>
          <div class="summary-grid">
            <div class="summary-item">
              <span class="label">رقم الطلب:</span>
              <span class="value">#${order.id}</span>
            </div>
            <div class="summary-item">
              <span class="label">تاريخ الطلب:</span>
              <span class="value">${formatDate(order.created_at)}</span>
            </div>
            <div class="summary-item">
              <span class="label">موعد التسليم:</span>
              <span class="value">${order.deliver_at ? formatDate(order.deliver_at) : "غير محدد"}</span>
            </div>
            <div class="summary-item">
              <span class="label">آخر تحديث:</span>
              <span class="value">${formatDate(order.status_updated_at)}</span>
            </div>
          </div>
        </div>

        <!-- Store Information -->
        <div class="section">
          <h3 class="section-title">معلومات المتجر</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">اسم المتجر:</span>
              <span class="value">${order.store.name}</span>
            </div>
            <div class="info-item">
              <span class="label">الوصف:</span>
              <span class="value">${order.store.description}</span>
            </div>
            <div class="info-item">
              <span class="label">العنوان:</span>
              <span class="value">${order.store.address}</span>
            </div>
            <div class="info-item">
              <span class="label">المدينة:</span>
              <span class="value">${order.store.city.name}, ${order.store.state.name}, ${order.store.country.name}</span>
            </div>
          </div>
        </div>

        <!-- Customer Information -->
        <div class="section">
          <h3 class="section-title">معلومات العميل</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">الاسم:</span>
              <span class="value">${order.store.owner.first_name} ${order.store.owner.last_name}</span>
            </div>
            <div class="info-item">
              <span class="label">اسم المستخدم:</span>
              <span class="value">${order.store.owner.username}</span>
            </div>
            <div class="info-item">
              <span class="label">البريد الإلكتروني:</span>
              <span class="value">${order.store.owner.email}</span>
            </div>
            <div class="info-item">
              <span class="label">رقم الهاتف:</span>
              <span class="value">${order.store.owner.phone}</span>
            </div>
          </div>
        </div>

        <!-- Order Items -->
        <div class="section">
          <h3 class="section-title">عناصر الطلب (${order.order_items.length} منتج)</h3>
          <table class="items-table">
            <thead>
              <tr>
                <th>المنتج</th>
                <th>الباركود</th>
                <th>الوحدة</th>
                <th>الكمية</th>
                <th>سعر الوحدة</th>
                <th>المجموع</th>
              </tr>
            </thead>
            <tbody>
              ${order.order_items.map(item => `
                <tr>
                  <td>
                    <div class="product-info">
                      <div class="product-title">${item.product.title}</div>
                      <div class="product-name">${item.product.name}</div>
                    </div>
                  </td>
                  <td>${item.product.barcode}</td>
                  <td>${item.product.unit} (${item.product.unit_count})</td>
                  <td>${item.quantity}</td>
                  <td>${formatCurrency(item.price_per_unit)}</td>
                  <td class="total-cell">${formatCurrency(item.total_price)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <!-- Order Totals -->
        <div class="section">
          <h3 class="section-title">إجمالي الطلب</h3>
          <div class="totals-section">
            <div class="total-row">
              <span class="total-label">إجمالي المنتجات:</span>
              <span class="total-value">${formatCurrency(order.products_total_price)}</span>
            </div>
            <div class="total-row">
              <span class="total-label">الرسوم:</span>
              <span class="total-value">${formatCurrency(order.fees)}</span>
            </div>
            <div class="total-row grand-total">
              <span class="total-label">المجموع الكلي:</span>
              <span class="total-value">${formatCurrency(order.total_price)}</span>
            </div>
            ${order.final_completed_price ? `
              <div class="total-row final-price">
                <span class="total-label">المبلغ النهائي المدفوع:</span>
                <span class="total-value">${formatCurrency(order.final_completed_price)}</span>
              </div>
            ` : ''}
          </div>
        </div>

        <!-- Status History -->
        ${(order.status_reason || order.completed_at) ? `
          <div class="section">
            <h3 class="section-title">تاريخ الحالة</h3>
            <div class="status-history">
              <div class="status-item">
                <span class="label">آخر تحديث:</span>
                <span class="value">${formatDate(order.status_updated_at)}</span>
              </div>
              <div class="status-item">
                <span class="label">بواسطة:</span>
                <span class="value">${order.status_updated_by.first_name} ${order.status_updated_by.last_name}</span>
              </div>
              ${order.status_reason ? `
                <div class="status-item">
                  <span class="label">السبب:</span>
                  <span class="value">${order.status_reason}</span>
                </div>
              ` : ''}
              ${order.completed_at ? `
                <div class="status-item">
                  <span class="label">تاريخ الإكمال:</span>
                  <span class="value">${formatDate(order.completed_at)}</span>
                </div>
              ` : ''}
            </div>
          </div>
        ` : ''}

        <!-- Footer -->
        <div class="print-footer">
          <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الطلبات</p>
        </div>
      </div>
    `
  }

  const getPrintStyles = (): string => {
    // Return the CSS styles as a string
    return `
      /* Print Styles */
      * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
      }

      body {
        font-family: 'Arial', sans-serif;
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
        direction: rtl;
        text-align: right;
        margin: 0;
        padding: 0;
      }

      @page {
        size: A4;
        margin: 2cm 1.5cm;
        direction: rtl;
      }

      .page-break-after {
        page-break-after: always;
      }

      .print-order {
        width: 100%;
        margin: 0;
        padding: 0;
        background: #fff;
        color: #000;
      }

      .print-header {
        margin-bottom: 30px;
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
      }

      .company-name {
        font-size: 24pt;
        font-weight: bold;
        margin: 0 0 10px 0;
        text-align: center;
      }

      .print-date {
        font-size: 10pt;
        margin: 0;
        text-align: left;
        color: #666;
      }

      .order-header {
        margin-bottom: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding-bottom: 10px;
      }

      .order-title {
        font-size: 18pt;
        font-weight: bold;
        margin: 0;
      }

      .order-status {
        font-size: 14pt;
      }

      .status-label {
        font-weight: bold;
      }

      .status-value {
        background: #f0f0f0;
        padding: 5px 10px;
        border-radius: 5px;
        border: 1px solid #ccc;
      }

      .section {
        margin-bottom: 25px;
        page-break-inside: avoid;
      }

      .section-title {
        font-size: 14pt;
        font-weight: bold;
        margin: 0 0 15px 0;
        padding: 8px 0;
        border-bottom: 1px solid #000;
        background: #f8f8f8;
        padding-right: 10px;
      }

      .summary-grid,
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
      }

      .summary-item,
      .info-item {
        display: flex;
        justify-content: space-between;
        padding: 8px;
        border: 1px solid #ddd;
        background: #fafafa;
      }

      .label {
        font-weight: bold;
        color: #333;
      }

      .value {
        color: #000;
      }

      .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
        font-size: 11pt;
      }

      .items-table th,
      .items-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: right;
      }

      .items-table th {
        background: #f0f0f0;
        font-weight: bold;
        font-size: 12pt;
      }

      .items-table tbody tr:nth-child(even) {
        background: #f9f9f9;
      }

      .product-info {
        line-height: 1.3;
      }

      .product-title {
        font-weight: bold;
        margin-bottom: 3px;
      }

      .product-name {
        font-size: 10pt;
        color: #666;
      }

      .total-cell {
        font-weight: bold;
        background: #f0f0f0 !important;
      }

      .totals-section {
        border: 2px solid #000;
        padding: 15px;
        background: #f8f8f8;
        margin-top: 20px;
      }

      .total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
      }

      .total-label {
        font-weight: bold;
      }

      .total-value {
        font-weight: bold;
      }

      .grand-total {
        border-top: 2px solid #000;
        border-bottom: 2px solid #000;
        margin-top: 10px;
        padding-top: 10px;
        padding-bottom: 10px;
        font-size: 14pt;
      }

      .final-price {
        background: #e8f5e8;
        padding: 8px;
        margin-top: 10px;
        border: 1px solid #4caf50;
        border-radius: 5px;
      }

      .final-price .total-value {
        color: #2e7d32;
      }

      .status-history {
        background: #f5f5f5;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      .status-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
      }

      .status-item:last-child {
        margin-bottom: 0;
      }

      .print-footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #ccc;
        text-align: center;
        font-size: 10pt;
        color: #666;
      }
    `
  }

  return (
    <PrintContext.Provider value={{
      printSingleOrder,
      printMultipleOrders,
      isPrinting
    }}>
      {children}
    </PrintContext.Provider>
  )
}

// Create context for print functionality
import { createContext, useContext } from "react"

interface PrintContextType {
  printSingleOrder: (order: DetailedOrder) => Promise<void>
  printMultipleOrders: (orders: WholesalerOrder[]) => Promise<void>
  isPrinting: boolean
}

const PrintContext = createContext<PrintContextType | undefined>(undefined)

export function usePrint() {
  const context = useContext(PrintContext)
  if (context === undefined) {
    throw new Error('usePrint must be used within a PrintUtility')
  }
  return context
}
