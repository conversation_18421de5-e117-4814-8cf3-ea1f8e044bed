import React, { createContext, useContext } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import type { ReactNode } from "react";
import { getCurrentUser, logout as apiLogout } from "./api/auth";
import type { UserResponse } from "./api/auth";

interface AuthContextType {
    user: UserResponse | null;
    isLoading: boolean;
    error: string | null;
    refetch: () => void;
    logout: () => void;
    isLoggedIn: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
    const queryClient = useQueryClient();
    // Read user from localStorage as a temporary fallback
    const tempUser = React.useMemo(() => {
        try {
            const raw = localStorage.getItem("user");
            return raw ? JSON.parse(raw) : null;
        } catch {
            return null;
        }
    }, []);
    const {
        data: user,
        isLoading,
        error,
        refetch,
    } = useQuery<UserResponse, Error>({ queryKey: ["currentUser"], queryFn: getCurrentUser });

    const handleLogout = () => {
        apiLogout();
        queryClient.removeQueries({ queryKey: ["currentUser"] });
        localStorage.removeItem("user");
    };

    // Use API user if available, otherwise fallback to tempUser
    const effectiveUser = user ?? tempUser;
    const isLoggedIn = Boolean(effectiveUser && effectiveUser.wholesaler_id);

    return (
        <AuthContext.Provider
            value={{
                user: effectiveUser ?? null,
                isLoading,
                error: error ? error.message : null,
                refetch,
                logout: handleLogout,
                isLoggedIn,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
}

export function useAuth() {
    const ctx = useContext(AuthContext);
    if (!ctx) throw new Error("useAuth must be used within an AuthProvider");
    return ctx;
} 