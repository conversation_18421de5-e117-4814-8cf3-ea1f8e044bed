import { baseUrl } from "./base";

// Login API for wholesaler authentication
export interface LoginRequest {
    phone: string;
    password: string;
}

export interface LoginResponse {
    success: boolean;
    token: string;
    user_id: number;
    phone: string;
    is_phone_verified: boolean;
    wholesaler_id?: number | null;
}

export async function login(data: LoginRequest): Promise<LoginResponse> {
    const res = await fetch(`${baseUrl}/login`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
    });
    if (!res.ok) {
        let message = "فشل تسجيل الدخول";
        try {
            const err = await res.json();
            message = err.detail || err.message || message;
        } catch { }
        throw new Error(message);
    }
    return res.json();
}

export interface UserResponse {
    id: number;
    username: string;
    email?: string | null;
    phone: string;
    phone_verified: boolean;
    first_name?: string | null;
    last_name?: string | null;
    is_active: boolean;
    date_joined: string;
    wholesaler_id?: number | null;
}

export async function getCurrentUser(): Promise<UserResponse> {
    const token = localStorage.getItem("token")
    if (!token) throw new Error("لم يتم تسجيل الدخول")
    const res = await fetch(`${baseUrl}/me`, {
        headers: {
            "Authorization": `Bearer ${token}`,
        },
    })
    if (!res.ok) {
        let message = "فشل جلب بيانات المستخدم";
        try {
            const err = await res.json();
            message = err.detail || err.message || message;
        } catch { }
        throw new Error(message);
    }
    return res.json();
}

export function logout() {
    localStorage.removeItem("token");
} 