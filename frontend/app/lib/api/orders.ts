import { baseUrl, makeAuthenticatedRequest } from "./base";

// Types for Order Management
export interface CustomUser {
    id: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    phone: string;
    phone_verified: boolean;
}

export interface Region {
    id: number;
    name: string;
    type: string;
    slug: string;
}

export interface Store {
    id: number;
    name: string;
    description: string;
    address: string;
    city: Region;
    state: Region;
    country: Region;
    owner: CustomUser;
    created_at: string;
    updated_at: string;
}

export interface Product {
    id: number;
    name: string;
    title: string;
    barcode: string;
    slug: string;
    description: string;
    image_url?: string | null;
    unit: string;
    unit_count: number;
}

export interface OrderItem {
    id: number;
    product: Product;
    quantity: number;
    price_per_unit: number;
    total_price: number;
}

export enum OrderStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SHIPPED = "shipped",
    DELIVERED = "delivered",
    CANCELLED = "cancelled"
}

export interface WholesalerOrder {
    id: number;
    store: Store;
    total_price: number;
    fees: number;
    deliver_at: string;
    products_total_price: number;
    products_total_quantity: number;
    status: OrderStatus;
    status_reason?: string | null;
    status_updated_at: string;
    status_updated_by?: CustomUser | null;
    completed_at?: string | null;
    final_completed_price?: number | null;
}

export interface WholesalerOrdersResponse {
    orders: WholesalerOrder[];
}

export interface OrderCompleteRequest {
    order_id: number;
    final_completed_price?: number | null;
}

export interface OrderActionResponse {
    success: boolean;
    error?: string;
}

// Detailed order interface for order details dialog
export interface DetailedOrder {
    id: number;
    store: Store;
    wholesaler_id: number;
    store_id: number;
    store_name: string;
    total_price: number;
    fees: number;
    deliver_at?: string | null;
    products_total_price: number;
    products_total_quantity: number;
    status: OrderStatus;
    status_reason?: string | null;
    status_updated_at: string;
    status_updated_by: CustomUser;
    created_at: string;
    updated_at: string;
    completed_at?: string | null;
    final_completed_price?: number | null;
    order_items: OrderItem[];
}

// API Functions

// Get wholesaler orders with pagination
export async function getWholesalerOrders(
    offset: number = 0,
    limit: number = 10
): Promise<WholesalerOrdersResponse> {
    const response = await makeAuthenticatedRequest(
        `${baseUrl}/wholesaler-orders/?offset=${offset}&limit=${limit}`
    );
    return response.json();
}

// Accept an order
export async function acceptOrder(orderId: number): Promise<OrderActionResponse> {
    const response = await makeAuthenticatedRequest(
        `${baseUrl}/wholesaler-orders/accept/${orderId}`,
        {
            method: "POST",
        }
    );
    return response.json();
}

// Reject an order
export async function rejectOrder(orderId: number): Promise<OrderActionResponse> {
    const response = await makeAuthenticatedRequest(
        `${baseUrl}/wholesaler-orders/reject/${orderId}`,
        {
            method: "POST",
        }
    );
    return response.json();
}

// Cancel an order
export async function cancelOrder(orderId: number): Promise<OrderActionResponse> {
    const response = await makeAuthenticatedRequest(
        `${baseUrl}/wholesaler-orders/cancel/${orderId}`,
        {
            method: "POST",
        }
    );
    return response.json();
}

// Complete an order
export async function completeOrder(data: OrderCompleteRequest): Promise<OrderActionResponse> {
    const response = await makeAuthenticatedRequest(
        `${baseUrl}/wholesaler-orders/complete`,
        {
            method: "POST",
            body: JSON.stringify(data),
        }
    );
    return response.json();
}

// Get detailed order information by ID
export async function getOrderById(orderId: number): Promise<DetailedOrder> {
    const response = await makeAuthenticatedRequest(
        `${baseUrl}/orders/${orderId}`
    );
    return response.json();
}

// Helper function to get order status in Arabic
export function getOrderStatusInArabic(status: OrderStatus): string {
    switch (status) {
        case OrderStatus.PENDING:
            return "في الانتظار";
        case OrderStatus.PROCESSING:
            return "قيد المعالجة";
        case OrderStatus.SHIPPED:
            return "تم الشحن";
        case OrderStatus.DELIVERED:
            return "تم التسليم";
        case OrderStatus.CANCELLED:
            return "ملغي";
        default:
            return status;
    }
}

// Helper function to get order status color
export function getOrderStatusColor(status: OrderStatus): string {
    switch (status) {
        case OrderStatus.PENDING:
            return "bg-yellow-100 text-yellow-800";
        case OrderStatus.PROCESSING:
            return "bg-blue-100 text-blue-800";
        case OrderStatus.SHIPPED:
            return "bg-purple-100 text-purple-800";
        case OrderStatus.DELIVERED:
            return "bg-green-100 text-green-800";
        case OrderStatus.CANCELLED:
            return "bg-red-100 text-red-800";
        default:
            return "bg-gray-100 text-gray-800";
    }
}
