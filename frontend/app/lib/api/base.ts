export const baseUrlV1 = "https://api.tagerplus.com/api";
export const baseUrl = "https://api.tagerplus.com/api/v2";

// Helper function to make authenticated requests
export async function makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    const token = localStorage.getItem("token");
    if (!token) {
        throw new Error("لم يتم تسجيل الدخول");
    }

    const response = await fetch(url, {
        ...options,
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`,
            ...options.headers,
        },
    });

    if (!response.ok) {
        let message = "حدث خطأ في الطلب";
        try {
            const error = await response.json();
            message = error.detail || error.message || message;
        } catch {
            // If response is not JSON, use status text
            message = response.statusText || message;
        }
        throw new Error(message);
    }

    return response;
}