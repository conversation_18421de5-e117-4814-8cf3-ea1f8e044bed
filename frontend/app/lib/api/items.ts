import { baseUrl, baseUrlV1, makeAuthenticatedRequest } from "./base";

// Types for Item Management
export interface Company {
    id: number;
    name: string;
}

export interface Category {
    id: number;
    name: string;
}

export interface Product {
    id: number;
    name: string;
    barcode: string;
    image_url?: string | null;
    company?: Company | null;
    category?: Category | null;
    unit: string;
    unit_count: number;
}

export interface WholesalerItem {
    id: number;
    product_id: number;
    product_name: string;
    price: number;
    inventory_count: number;
    minimum_order_quantity: number;
    maximum_order_quantity?: number | null;
    price_expiry?: string | null;
    image_url?: string | null;
    company_id?: number | null;
    category_id?: number | null;
    company?: Company | null;
    category?: Category | null;
    unit: string;
    unit_count: number;
}

export interface ItemOut {
    id: number;
    product: {
        id: number;
        name: string;
        barcode: string;
        image_url?: string | null;
    };
    base_price: number;
    inventory_count: number;
    minimum_order_quantity: number;
    maximum_order_quantity?: number | null;
    price_expiry?: string | null;
    created_at: string;
}

export interface CreateItemRequest {
    product_id: number;
    base_price: number;
    inventory_count?: number;
    minimum_order_quantity?: number;
    maximum_order_quantity?: number | null;
    price_expiry?: string | null;
}

export interface UpdateItemRequest {
    base_price?: number;
    inventory_count?: number;
    minimum_order_quantity?: number;
    maximum_order_quantity?: number | null;
    price_expiry?: string | null;
}

export interface CategoriesCompaniesResponse {
    categories: Array<{ id: number; name: string }>;
    companies: Array<{ id: number; name: string }>;
}

export interface ProductsResponse {
    items: Product[];
    total: number;
    page: number;
    size: number;
    pages: number;
}

// API Functions

// Get categories and companies for dropdowns
export async function getCategoriesAndCompanies(): Promise<CategoriesCompaniesResponse> {
    const response = await makeAuthenticatedRequest(`${baseUrl}/categories-companies/`);
    return response.json();
}

// Get products with search and filtering using list_search_products endpoint
export async function getProducts(params?: {
    search?: string;
    company_id?: number;
    category_id?: number;
    page?: number;
    size?: number;
}): Promise<ProductsResponse> {
    const searchParams = new URLSearchParams();

    if (params?.search) searchParams.append("search", params.search);
    if (params?.company_id) searchParams.append("company_id", params.company_id.toString());
    if (params?.category_id) searchParams.append("category_id", params.category_id.toString());
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.size) searchParams.append("size", params.size.toString());

    const url = `${baseUrlV1}/products/${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;
    const response = await makeAuthenticatedRequest(url);
    return response.json();
}

// Get my wholesaler items (using v1 API for authenticated user)
export async function getMyItems(): Promise<ItemOut[]> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items`);
    return response.json();
}

// Create a new item
export async function createItem(data: CreateItemRequest): Promise<ItemOut> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items`, {
        method: "POST",
        body: JSON.stringify(data),
    });
    return response.json();
}

// Update an existing item
export async function updateItem(itemId: number, data: UpdateItemRequest): Promise<ItemOut> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items/${itemId}`, {
        method: "PUT",
        body: JSON.stringify(data),
    });
    return response.json();
}

// Delete an item
export async function deleteItem(itemId: number): Promise<{ success: boolean; message: string }> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items/${itemId}`, {
        method: "DELETE",
    });
    return response.json();
}

// Get a specific item
export async function getItem(itemId: number): Promise<ItemOut> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items/${itemId}`);
    return response.json();
}

// Inventory management functions
export async function updateInventory(
    itemId: number,
    data: {
        quantity: number;
        transaction_type: "ADDITION" | "SUBTRACTION";
        notes?: string
    }
): Promise<{ success: boolean; message: string; new_inventory_count: number }> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items/${itemId}/inventory`, {
        method: "POST",
        body: JSON.stringify(data),
    });
    return response.json();
}

// Expire item pricing
export async function expireItem(itemId: number): Promise<{ success: boolean; message: string }> {
    const response = await makeAuthenticatedRequest(`${baseUrlV1}/wholesalers/me/items/${itemId}/expire`, {
        method: "PUT",
    });
    return response.json();
}
