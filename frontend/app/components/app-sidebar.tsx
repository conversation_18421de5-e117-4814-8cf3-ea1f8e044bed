import * as React from "react"
import {
  AudioWaveform,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  Package,
  ShoppingCart,
  BarChart3,
} from "lucide-react"

import { NavMain } from "~/components/nav-main"
import { NavProjects } from "~/components/nav-projects"
import { NavUser } from "~/components/nav-user"
import { TeamSwitcher } from "~/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "~/components/ui/sidebar"
import { useAuth } from "~/lib/auth-context"

// This is sample data.
const data = {
  user: {
    name: "شادكن",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
  ],
  navMain: [
    {
      title: "إدارة المنتجات",
      url: "/items",
      icon: Package,
      isActive: true,
      items: [
        {
          title: "قائمة المنتجات",
          url: "/items",
        },
      ],
    },
    {
      title: "الطلبات",
      url: "/dashboard",
      icon: ShoppingCart,
      isActive: true,
      items: [
        {
          title: "جميع الطلبات",
          url: "/dashboard",
        },
        // {
        //   title: "الطلبات الواردة",
        //   url: "/orders?status=pending",
        // },
        // {
        //   title: "الطلبات المكتملة",
        //   url: "/orders?status=delivered",
        // },
        // {
        //   title: "الطلبات الملغية",
        //   url: "/orders?status=cancelled",
        // },
      ],
    },
  ],
  projects: [
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuth()
  // Fallback avatar: use initials if no avatar
  const navUser = user
    ? {
      name: user.first_name || user.username || user.phone,
      email: user.email || user.phone,
      avatar: "/avatars/shadcn.jpg", // TODO: replace with user.avatar if available
    }
    : null
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <img src="/logo_cropped.png" alt="Logo" style={{ width: 124, height: 124, objectFit: 'contain', margin: '0 auto 0px auto', display: 'block' }} />
        <TeamSwitcher teams={[]} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        {/* <NavProjects projects={data.projects} /> */}
      </SidebarContent>
      <SidebarFooter>
        {navUser && <NavUser user={navUser} />}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
