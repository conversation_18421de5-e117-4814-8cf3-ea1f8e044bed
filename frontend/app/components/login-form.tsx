import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { useNavigate } from "react-router"
import { login as loginApi } from "~/lib/api/auth"

import { cn } from "~/lib/utils"
import { But<PERSON> } from "~/components/ui/button"
import { Input } from "~/components/ui/input"
import { Label } from "~/components/ui/label"
import { GalleryVerticalEnd } from "lucide-react"
import { useAuth } from "~/lib/auth-context"

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const { refetch } = useAuth()
  const [phone, setPhone] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const navigate = useNavigate()
  const mutation = useMutation({
    mutationFn: loginApi,
    onSuccess: (data: import("~/lib/api/auth").LoginResponse) => {
      if (!data.wholesaler_id) {
        setError("هذا التطبيق مخصص فقط للوكلاء. حسابك غير مصرح له بالدخول.")
        return
      }
      localStorage.setItem("token", data.token)
      localStorage.setItem("user", JSON.stringify({
        user_id: data.user_id,
        phone: data.phone,
        is_phone_verified: data.is_phone_verified,
        wholesaler_id: data.wholesaler_id,
      }))

      refetch()
      navigate("/dashboard")
    },
    onError: (err: any) => {
      setError(err?.message || "حدث خطأ أثناء تسجيل الدخول")
    },
  })
  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <form
        onSubmit={e => {
          e.preventDefault()
          setError("")
          mutation.mutate({ phone, password })
        }}
      >
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center gap-2">
            <div className="flex size-8 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-6" />
            </div>
            <h1 className="text-xl font-bold">تسجيل دخول الوكلاء</h1>
          </div>
          <div className="flex flex-col gap-6">
            <div className="grid gap-3">
              <Label htmlFor="phone">رقم الجوال</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="05xxxxxxxx"
                value={phone}
                onChange={e => setPhone(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-3">
              <Label htmlFor="password">كلمة المرور</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
              />
            </div>
            {error && <div className="text-red-500 text-sm text-center">{error}</div>}
            <Button type="submit" className="w-full" disabled={mutation.isPending}>
              {mutation.isPending ? "جاري الدخول..." : "تسجيل الدخول"}
            </Button>
          </div>
        </div>
      </form>
      <div className="text-muted-foreground text-center text-xs text-balance">
        بالنقر على تسجيل الدخول، فإنك توافق على <a href="#" className="underline underline-offset-4">شروط الخدمة</a> و <a href="#" className="underline underline-offset-4">سياسة الخصوصية</a>.
      </div>
    </div>
  )
}
