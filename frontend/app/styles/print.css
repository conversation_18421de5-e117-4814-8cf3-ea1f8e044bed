/* Print Styles for Orders Management System */

@media print {
  /* Reset and base styles */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  body {
    font-family: 'Arial', sans-serif;
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
    direction: rtl;
    text-align: right;
  }

  /* Hide non-printable elements */
  .no-print,
  .sidebar,
  .header,
  .navigation,
  button:not(.print-button),
  .dialog-overlay,
  .dialog-close {
    display: none !important;
  }

  /* Page setup */
  @page {
    size: A4;
    margin: 2cm 1.5cm;
    direction: rtl;
  }

  /* Page breaks */
  .page-break-after {
    page-break-after: always;
  }

  .page-break-before {
    page-break-before: always;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid;
  }

  /* Print order container */
  .print-order {
    width: 100%;
    margin: 0;
    padding: 0;
    background: #fff;
    color: #000;
  }

  /* Header styles */
  .print-header {
    margin-bottom: 30px;
    border-bottom: 2px solid #000;
    padding-bottom: 15px;
  }

  .company-name {
    font-size: 24pt;
    font-weight: bold;
    margin: 0 0 10px 0;
    text-align: center;
  }

  .print-date {
    font-size: 10pt;
    margin: 0;
    text-align: left;
    color: #666;
  }

  /* Order header */
  .order-header {
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
  }

  .order-title {
    font-size: 18pt;
    font-weight: bold;
    margin: 0;
  }

  .order-status {
    font-size: 14pt;
  }

  .status-label {
    font-weight: bold;
  }

  .status-value {
    background: #f0f0f0;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
  }

  /* Section styles */
  .section {
    margin-bottom: 25px;
    page-break-inside: avoid;
  }

  .section-title {
    font-size: 14pt;
    font-weight: bold;
    margin: 0 0 15px 0;
    padding: 8px 0;
    border-bottom: 1px solid #000;
    background: #f8f8f8;
    padding-right: 10px;
  }

  /* Grid layouts */
  .summary-grid,
  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
  }

  .summary-item,
  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    border: 1px solid #ddd;
    background: #fafafa;
  }

  .label {
    font-weight: bold;
    color: #333;
  }

  .value {
    color: #000;
  }

  /* Table styles */
  .items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 11pt;
  }

  .items-table th,
  .items-table td {
    border: 1px solid #000;
    padding: 8px;
    text-align: right;
  }

  .items-table th {
    background: #f0f0f0;
    font-weight: bold;
    font-size: 12pt;
  }

  .items-table tbody tr:nth-child(even) {
    background: #f9f9f9;
  }

  .product-info {
    line-height: 1.3;
  }

  .product-title {
    font-weight: bold;
    margin-bottom: 3px;
  }

  .product-name {
    font-size: 10pt;
    color: #666;
  }

  .total-cell {
    font-weight: bold;
    background: #f0f0f0 !important;
  }

  /* Totals section */
  .totals-section {
    border: 2px solid #000;
    padding: 15px;
    background: #f8f8f8;
    margin-top: 20px;
  }

  .total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
  }

  .total-label {
    font-weight: bold;
  }

  .total-value {
    font-weight: bold;
  }

  .grand-total {
    border-top: 2px solid #000;
    border-bottom: 2px solid #000;
    margin-top: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 14pt;
  }

  .final-price {
    background: #e8f5e8;
    padding: 8px;
    margin-top: 10px;
    border: 1px solid #4caf50;
    border-radius: 5px;
  }

  .final-price .total-value {
    color: #2e7d32;
  }

  /* Status history */
  .status-history {
    background: #f5f5f5;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
  }

  .status-item:last-child {
    margin-bottom: 0;
  }

  /* Footer */
  .print-footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #ccc;
    text-align: center;
    font-size: 10pt;
    color: #666;
  }

  /* Utility classes */
  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .font-bold {
    font-weight: bold;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  /* Ensure proper spacing between multiple orders */
  .print-order:not(:last-child) {
    margin-bottom: 0;
  }

  /* Hide screen-only elements */
  .screen-only {
    display: none !important;
  }
}

/* Screen styles for print preview */
@media screen {
  .print-preview {
    background: #f5f5f5;
    padding: 20px;
    min-height: 100vh;
  }

  .print-order {
    background: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    padding: 40px;
    max-width: 210mm;
    min-height: 297mm;
    direction: rtl;
    text-align: right;
  }

  .print-order.page-break-after {
    margin-bottom: 40px;
  }
}
