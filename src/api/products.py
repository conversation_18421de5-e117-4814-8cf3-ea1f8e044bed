from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from ninja import Schema
from django.http import Http404
from django.contrib.postgres.search import SearchQuery

from products.models import Product, Region
from wholesalers.models import Item, RegionMinCharge
from products.home_views import _get_region_hierarchy


class WholesalerOut(Schema):
    id: int
    category: str
    title: str
    username: str
    logo_url: Optional[str] = None
    background_image_url: Optional[str] = None


class CompanyOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class CategoryOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class ProductPriceInfo(Schema):
    price: Decimal
    wholesaler_id: int
    wholesaler: WholesalerOut
    inventory_count: int
    price_expiry: Optional[datetime] = None
    item_id: int


class ProductResponse(Schema):
    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: str = None
    company_id: int
    category_id: int
    company: CompanyOut
    category: CategoryOut
    unit: str
    unit_count: int
    prices: List[ProductPriceInfo]


# GET: /api/v2/products/{product_id}
def get_product_by_id(request, product_id: int, region_id: int) -> ProductResponse:
    """
    Get a product by its ID and its prices from all wholesalers in the same region or parent regions.

    Args:
        product_id: The ID of the product to retrieve
        region_id: The ID of the region to retrieve prices from (includes parent regions)

    Returns:
        ProductResponse: The product with its prices from the region hierarchy
    """
    # Get the product with related company and category data
    try:
        product = (
            Product.objects.select_related("company", "category")
            .filter(deleted_at__isnull=True)
            .get(id=product_id)
        )
    except Product.DoesNotExist:
        raise Http404("Product not found")

    # Get all wholesalers that serve the specified region or its parent regions
    # First, get the target region and build hierarchy of region IDs
    try:
        target_region = Region.objects.select_related("parent").get(
            id=region_id, deleted_at__isnull=True
        )
    except Region.DoesNotExist:
        raise Http404("Region not found")
    region_ids = [region_id]

    # Add all parent regions to the list
    current_region = target_region
    while current_region.parent:
        current_region = Region.objects.get(id=current_region.parent.id)
        region_ids.append(current_region.id)

    # Get wholesaler IDs from all regions in the hierarchy
    wholesaler_ids_in_region = list(
        RegionMinCharge.objects.filter(
            region_id__in=region_ids, deleted_at__isnull=True
        ).values_list("wholesaler_id", flat=True)
    )

    # Get all items (prices) for this product from wholesalers in the region
    items_with_wholesalers = list(
        Item.objects.select_related("wholesaler").filter(
            product_id=product_id,
            wholesaler_id__in=wholesaler_ids_in_region,
            deleted_at__isnull=True,
            wholesaler__deleted_at__isnull=True,
        )
    )

    # Build the price information list
    prices = []
    for item in items_with_wholesalers:
        price_info = ProductPriceInfo(
            price=item.base_price,
            wholesaler_id=item.wholesaler.id,
            wholesaler=WholesalerOut(
                id=item.wholesaler.id,
                category=item.wholesaler.category,
                title=item.wholesaler.title,
                username=item.wholesaler.username,
                background_image_url=item.wholesaler.background_image.url
                if item.wholesaler.background_image
                else None,
                logo_url=item.wholesaler.logo.url if item.wholesaler.logo else None,
            ),
            inventory_count=item.inventory_count,
            price_expiry=item.price_expiry,
            item_id=item.id,
        )
        prices.append(price_info)

    # Build the response
    response = ProductResponse(
        id=product.id,
        name=product.name,
        title=product.title,
        barcode=product.barcode,
        slug=product.slug,
        description=product.description,
        image_url=product.image.url if product.image else None,
        company_id=product.company.id if product.company else None,
        category_id=product.category.id if product.category else None,
        company=CompanyOut(
            id=product.company.id,
            name=product.company.name,
            title=product.company.title,
            slug=product.company.slug,
        )
        if product.company
        else None,
        category=CategoryOut(
            id=product.category.id,
            name=product.category.name,
            title=product.category.title,
            slug=product.category.slug,
        )
        if product.category
        else None,
        unit=product.unit,
        unit_count=int(product.unit_count),
        prices=prices,
    )

    return response


# GET: /api/v2/products/company/{company_id}
def get_products_by_company_id(
    request, company_id: int, region_id: int = None
) -> list[ProductResponse]:
    base_queryset = Product.objects.filter(
        company_id=company_id, deleted_at__isnull=True
    )
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        # Only include products that have at least one active wholesaler item in the region or its parents
        base_queryset = base_queryset.filter(
            wholesalers__deleted_at__isnull=True,
            wholesalers__inventory_count__gt=0,
            wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
            wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
        ).distinct()
    products = base_queryset
    return [ProductResponse(product) for product in products]


# GET: /api/v2/products/category/{category_id}
def get_products_by_category_id(
    request, category_id: int, region_id: int = None
) -> list[ProductResponse]:
    base_queryset = Product.objects.filter(
        category_id=category_id, deleted_at__isnull=True
    )
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        base_queryset = base_queryset.filter(
            wholesalers__deleted_at__isnull=True,
            wholesalers__inventory_count__gt=0,
            wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
            wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
        ).distinct()
    products = base_queryset
    return [ProductResponse(product) for product in products]


# GET: /api/v2/products/search
def search_products(
    request, query: str, region_id: int = None
) -> list[ProductResponse]:
    search_query = SearchQuery(query, config="arabic")

    base_queryset = Product.objects.filter(
        deleted_at__isnull=True,
        search_vector=search_query,
        items_count__gt=0,
    )
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        base_queryset = base_queryset.filter(
            wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
            wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
        )
    products = base_queryset.distinct()
    result = []
    for product in products:
        # Get all items for this product (not deleted, inventory > 0)
        items = Item.objects.select_related("wholesaler").filter(
            product_id=product.id,
            deleted_at__isnull=True,
            inventory_count__gt=0,
            wholesaler__deleted_at__isnull=True,
        )
        if not items:
            continue  # Skip products with no items
        prices = []
        for item in items:
            price_info = ProductPriceInfo(
                price=item.base_price,
                wholesaler_id=item.wholesaler.id,
                wholesaler=WholesalerOut(
                    id=item.wholesaler.id,
                    category=item.wholesaler.category,
                    title=item.wholesaler.title,
                    username=item.wholesaler.username,
                    background_image_url=item.wholesaler.background_image.url
                    if item.wholesaler.background_image
                    else None,
                    logo_url=item.wholesaler.logo.url if item.wholesaler.logo else None,
                ),
                inventory_count=item.inventory_count,
                price_expiry=item.price_expiry,
                item_id=item.id,
            )
            prices.append(price_info)
        product_response = ProductResponse(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            slug=product.slug,
            description=product.description,
            image_url=product.image.url if product.image else None,
            company_id=product.company.id if product.company else None,
            category_id=product.category.id if product.category else None,
            company=CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
                slug=product.company.slug,
            )
            if product.company
            else None,
            category=CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
                slug=product.category.slug,
            )
            if product.category
            else None,
            unit=product.unit,
            unit_count=int(product.unit_count),
            prices=prices,
        )
        result.append(product_response)
    return result
