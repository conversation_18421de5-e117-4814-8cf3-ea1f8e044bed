from typing import List, Optional
from ninja import Schema
from products.models import Region, RegionType


class RegionWithHierarchyOut(Schema):
    id: int
    name: str
    type: str
    code: str
    parent_id: Optional[int] = None
    hierarchical_name: str


# GET: /api/v2/regions
def list_regions(request) -> List[RegionWithHierarchyOut]:
    """List all regions with hierarchical names"""
    regions = Region.objects.filter(deleted_at__isnull=True)
    print("Regions: ", regions)

    result = []
    for region in regions:
        # Build hierarchical name (child - parent - grandparent)
        hierarchical_name = region.name
        current = region

        # Traverse up the parent hierarchy
        while current.parent:
            current = current.parent
            hierarchical_name = f"{hierarchical_name} - {current.name}"

        result.append(
            RegionWithHierarchyOut(
                id=region.id,
                name=region.name,
                type=region.type,
                code=region.code,
                parent_id=region.parent.id if region.parent else None,
                hierarchical_name=hierarchical_name,
            )
        )

    return result


# GET: /api/v2/regions/countries
def get_countries(request) -> List[RegionWithHierarchyOut]:
    """List all countries with hierarchical names"""
    regions = Region.objects.filter(
        deleted_at__isnull=True, parent__isnull=True, type=RegionType.COUNTRY
    )

    result = []
    for region in regions:
        result.append(
            RegionWithHierarchyOut(
                id=region.id,
                name=region.name,
                type=region.type,
                code=region.code,
                parent_id=region.parent.id if region.parent else None,
                hierarchical_name=region.name,
            )
        )

    return result


# GET: /api/v2/regions/states
def get_states(request) -> List[RegionWithHierarchyOut]:
    """List all states with hierarchical names"""
    regions = Region.objects.filter(
        deleted_at__isnull=True, parent__isnull=False, type=RegionType.STATE
    )

    result = []
    for region in regions:
        result.append(
            RegionWithHierarchyOut(
                id=region.id,
                name=region.name,
                type=region.type,
                code=region.code,
                parent_id=region.parent.id if region.parent else None,
                hierarchical_name=region.name,
            )
        )

    return result


# GET: /api/v2/regions/districts
def get_districts(request) -> List[RegionWithHierarchyOut]:
    """List all districts with hierarchical names"""
    regions = Region.objects.filter(
        deleted_at__isnull=True, parent__isnull=False, type=RegionType.DISTRICT
    )

    result = []
    for region in regions:
        result.append(
            RegionWithHierarchyOut(
                id=region.id,
                name=region.name,
                type=region.type,
                code=region.code,
                parent_id=region.parent.id if region.parent else None,
                hierarchical_name=region.name,
            )
        )

    return result
