from typing import List, Optional

from ninja import Schema
from ninja.errors import HttpError
from django.shortcuts import get_object_or_404
from api.stores import RegionOut, StoreOut, CustomUserOut
from core.settings import FEES_PERCENTAGE
from wholesalers.models import (
    Wholesaler,
    Item,
    InventoryTransaction,
    InventoryTransactionType,
)
from stores.models import Order, OrderItem, Store, OrderStatus
from django.db import transaction
from django.utils import timezone
from accounts.utils import send_notification


class ProductBaseOut(Schema):
    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str]
    unit: str
    unit_count: int


class OrderItemIn(Schema):
    item_id: int
    quantity: int


class OrderItemOut(Schema):
    id: int
    product: ProductBaseOut
    quantity: int
    price_per_unit: float
    total_price: float


class OrderIn(Schema):
    wholesaler_id: int
    store_id: int
    items: List[OrderItemIn]
    deliver_at: Optional[str] = None


class OrderOut(Schema):
    id: int
    store: StoreOut
    wholesaler_id: int
    store_id: int
    store_name: str
    total_price: float
    fees: float
    deliver_at: Optional[str]
    products_total_price: float
    products_total_quantity: int
    status: OrderStatus
    status_reason: Optional[str]
    status_updated_at: str
    status_updated_by: CustomUserOut
    created_at: str
    updated_at: str
    order_items: List[OrderItemOut]


class OrderStatusUpdateIn(Schema):
    status: str
    status_reason: Optional[str] = None


class PaginatedOrderResponse(Schema):
    total: int
    items: List[OrderOut]


# POST: /api/v2/orders/
def create_order(request, order_in: OrderIn) -> OrderOut:
    with transaction.atomic():
        store = get_object_or_404(
            Store, id=order_in.store_id, owner=request.auth, deleted_at__isnull=True
        )

        wholesaler = get_object_or_404(
            Wholesaler, id=order_in.wholesaler_id, deleted_at__isnull=True
        )

        products_total_price = 0.00
        products_total_quantity = 0
        order_items_to_create = []

        for item_data in order_in.items:
            item = get_object_or_404(
                Item,
                id=item_data.item_id,
                wholesaler=wholesaler,
                deleted_at__isnull=True,
            )
            if not item:
                raise HttpError(404, f"Item not found for product: {item_data.item_id}")

            # Validate minimum order quantity
            if item_data.quantity < item.minimum_order_quantity:
                raise HttpError(
                    400,
                    f"الكمية المطلوبة للمنتج {item.product.title} أقل من الحد الأدنى المطلوب: {item_data.quantity} < {item.minimum_order_quantity}",
                )

            # Validate maximum order quantity (if set)
            if (
                item.maximum_order_quantity
                and item_data.quantity > item.maximum_order_quantity
            ):
                raise HttpError(
                    400,
                    f"الكمية المطلوبة للمنتج {item.product.title} أكبر من الحد الأقصى المسموح: {item_data.quantity} > {item.maximum_order_quantity}",
                )

            price_per_unit = item.base_price.__float__()
            total_item_price = price_per_unit * item_data.quantity
            products_total_price += total_item_price
            products_total_quantity += item_data.quantity

            # minus the quantity from the product
            if item.inventory_count < item_data.quantity:
                raise HttpError(
                    400,
                    f"الكمية المتاحة للمنتج {item.product.title} أقل من الكمية المطلوبة: {item.inventory_count} < {item_data.quantity}",
                )

            item.inventory_count -= item_data.quantity
            item.save()

            # add transaction to the item
            InventoryTransaction.objects.create(
                item=item,
                transaction_type=InventoryTransactionType.SUBTRACTION,
                quantity=item_data.quantity,
                notes="Inventory adjusted via order creation",
            )

            order_items_to_create.append(
                OrderItem(
                    product_item=item,
                    quantity=item_data.quantity,
                    price_per_unit=price_per_unit,
                    total_price=total_item_price,
                )
            )

        # Fees
        fees = products_total_price * (FEES_PERCENTAGE / 100)

        total_price = products_total_price + fees

        # Parse deliver_at if provided
        deliver_at = None
        if order_in.deliver_at:
            from datetime import datetime

            deliver_at = datetime.fromisoformat(
                order_in.deliver_at.replace("Z", "+00:00")
            )

        order = Order.objects.create(
            store=store,
            wholesaler=wholesaler,
            total_price=total_price,
            fees=fees,
            deliver_at=deliver_at,
            products_total_price=products_total_price,
            products_total_quantity=products_total_quantity,
            status_updated_by=request.auth,
        )

        for order_item in order_items_to_create:
            order_item.order = order
        OrderItem.objects.bulk_create(order_items_to_create)

        # Send notification to the store owner
        try:
            send_notification(
                wholesaler.user.id,
                "طلب جديد",
                f"لديك طلب جديد من {wholesaler.title}",
            )
        except Exception:
            pass

        # Retrieve the created order with its items to return in the response
        created_order_with_items = (
            Order.objects.select_related("status_updated_by")
            .prefetch_related("order_items__product_item__product")
            .get(id=order.id)
        )

        return OrderOut(
            id=created_order_with_items.id,
            wholesaler_id=created_order_with_items.wholesaler.id,
            store_id=created_order_with_items.store.id,
            store_name=created_order_with_items.store.name,
            total_price=created_order_with_items.total_price,
            fees=created_order_with_items.fees,
            deliver_at=created_order_with_items.deliver_at.isoformat()
            if created_order_with_items.deliver_at
            else None,
            products_total_price=created_order_with_items.products_total_price,
            products_total_quantity=created_order_with_items.products_total_quantity,
            status=created_order_with_items.status,
            status_reason=created_order_with_items.status_reason,
            status_updated_at=created_order_with_items.status_updated_at.isoformat(),
            status_updated_by=CustomUserOut(
                id=created_order_with_items.status_updated_by.id,
                username=created_order_with_items.status_updated_by.username,
                email=created_order_with_items.status_updated_by.email,
                first_name=created_order_with_items.status_updated_by.first_name,
                last_name=created_order_with_items.status_updated_by.last_name,
                phone=created_order_with_items.status_updated_by.phone,
                phone_verified=created_order_with_items.status_updated_by.phone_verified,
            ),
            created_at=created_order_with_items.created_at.isoformat(),
            updated_at=created_order_with_items.updated_at.isoformat(),
            store=StoreOut(
                id=created_order_with_items.store.id,
                name=created_order_with_items.store.name,
                description=created_order_with_items.store.description,
                address=created_order_with_items.store.address,
                owner=CustomUserOut(
                    id=created_order_with_items.store.owner.id,
                    username=created_order_with_items.store.owner.username,
                    email=created_order_with_items.store.owner.email,
                    first_name=created_order_with_items.store.owner.first_name,
                    last_name=created_order_with_items.store.owner.last_name,
                    phone=created_order_with_items.store.owner.phone,
                    phone_verified=created_order_with_items.store.owner.phone_verified,
                ),
                city=RegionOut(
                    id=created_order_with_items.store.city.id,
                    name=created_order_with_items.store.city.name,
                    type=created_order_with_items.store.city.type,
                    slug=created_order_with_items.store.city.slug,
                ),
                state=RegionOut(
                    id=created_order_with_items.store.state.id,
                    name=created_order_with_items.store.state.name,
                    type=created_order_with_items.store.state.type,
                    slug=created_order_with_items.store.state.slug,
                ),
                country=RegionOut(
                    id=created_order_with_items.store.country.id,
                    name=created_order_with_items.store.country.name,
                    type=created_order_with_items.store.country.type,
                    slug=created_order_with_items.store.country.slug,
                ),
                created_at=created_order_with_items.store.created_at.isoformat(),
                updated_at=created_order_with_items.store.updated_at.isoformat(),
            ),
            order_items=[
                OrderItemOut(
                    id=item.id,
                    product=ProductBaseOut(
                        id=item.product_item.product.id,
                        name=item.product_item.product.name,
                        title=item.product_item.product.title,
                        barcode=item.product_item.product.barcode,
                        slug=item.product_item.product.slug,
                        description=item.product_item.product.description,
                        image_url=item.product_item.product.image.url
                        if item.product_item.product.image
                        else None,
                        unit=item.product_item.product.unit,
                        unit_count=item.product_item.product.unit_count,
                    ),
                    quantity=item.quantity,
                    price_per_unit=item.price_per_unit,
                    total_price=item.total_price,
                )
                for item in created_order_with_items.order_items.all()
            ],
        )


# GET: /api/v2/orders/my
def get_my_orders(request, offset: int = 0, limit: int = 10) -> PaginatedOrderResponse:
    """
    Get all orders for the authenticated user.
    """
    orders = (
        Order.objects.filter(store__owner=request.auth, deleted_at__isnull=True)
        .select_related("status_updated_by", "store")
        .prefetch_related("order_items__product_item__product")
        .order_by("-created_at")
    )
    total = orders.count()
    orders = orders[offset : offset + limit]

    return PaginatedOrderResponse(
        total=total,
        items=[
            OrderOut(
                id=order.id,
                wholesaler_id=order.wholesaler.id,
                store_id=order.store.id,
                store_name=order.store.name,
                total_price=order.total_price,
                fees=order.fees,
                deliver_at=order.deliver_at.isoformat() if order.deliver_at else None,
                products_total_price=order.products_total_price,
                products_total_quantity=order.products_total_quantity,
                status=order.status,
                status_reason=order.status_reason,
                status_updated_at=order.status_updated_at.isoformat(),
                status_updated_by=CustomUserOut(
                    id=order.status_updated_by.id,
                    username=order.status_updated_by.username,
                    email=order.status_updated_by.email,
                    first_name=order.status_updated_by.first_name,
                    last_name=order.status_updated_by.last_name,
                    phone=order.status_updated_by.phone,
                    phone_verified=order.status_updated_by.phone_verified,
                ),
                created_at=order.created_at.isoformat(),
                updated_at=order.updated_at.isoformat(),
                store=StoreOut(
                    id=order.store.id,
                    name=order.store.name,
                    description=order.store.description,
                    address=order.store.address,
                    owner=CustomUserOut(
                        id=order.store.owner.id,
                        username=order.store.owner.username,
                        email=order.store.owner.email,
                        first_name=order.store.owner.first_name,
                        last_name=order.store.owner.last_name,
                        phone=order.store.owner.phone,
                        phone_verified=order.store.owner.phone_verified,
                    ),
                    city=RegionOut(
                        id=order.store.city.id,
                        name=order.store.city.name,
                        type=order.store.city.type,
                        slug=order.store.city.slug,
                    ),
                    state=RegionOut(
                        id=order.store.state.id,
                        name=order.store.state.name,
                        type=order.store.state.type,
                        slug=order.store.state.slug,
                    ),
                    country=RegionOut(
                        id=order.store.country.id,
                        name=order.store.country.name,
                        type=order.store.country.type,
                        slug=order.store.country.slug,
                    ),
                    created_at=order.store.created_at.isoformat(),
                    updated_at=order.store.updated_at.isoformat(),
                ),
                order_items=[
                    OrderItemOut(
                        id=item.id,
                        product=ProductBaseOut(
                            id=item.product_item.product.id,
                            name=item.product_item.product.name,
                            title=item.product_item.product.title,
                            barcode=item.product_item.product.barcode,
                            slug=item.product_item.product.slug,
                            description=item.product_item.product.description,
                            image_url=item.product_item.product.image.url
                            if item.product_item.product.image
                            else None,
                            unit=item.product_item.product.unit,
                            unit_count=item.product_item.product.unit_count,
                        ),
                        quantity=item.quantity,
                        price_per_unit=item.price_per_unit,
                        total_price=item.total_price,
                    )
                    for item in order.order_items.all()
                ],
            )
            for order in orders
        ],
    )


# GET: /api/v2/orders/{order_id}
def get_order_by_id(request, order_id: int) -> OrderOut:
    """
    Get a single order by its ID for a specific store owned by the authenticated user.
    """
    order = get_object_or_404(
        Order.objects.select_related("status_updated_by").prefetch_related(
            "order_items__product_item__product"
        ),
        id=order_id,
        deleted_at__isnull=True,
    )

    if order.store.owner != request.auth and order.wholesaler.user != request.auth:
        raise HttpError(403, "You are not authorized to access this order")

    return OrderOut(
        id=order.id,
        wholesaler_id=order.wholesaler.id,
        store_id=order.store.id,
        store_name=order.store.name,
        total_price=order.total_price,
        fees=order.fees,
        deliver_at=order.deliver_at.isoformat() if order.deliver_at else None,
        products_total_price=order.products_total_price,
        products_total_quantity=order.products_total_quantity,
        status=order.status,
        status_reason=order.status_reason,
        status_updated_at=order.status_updated_at.isoformat(),
        status_updated_by=CustomUserOut(
            id=order.status_updated_by.id,
            username=order.status_updated_by.username,
            email=order.status_updated_by.email,
            first_name=order.status_updated_by.first_name,
            last_name=order.status_updated_by.last_name,
            phone=order.status_updated_by.phone,
            phone_verified=order.status_updated_by.phone_verified,
        ),
        created_at=order.created_at.isoformat(),
        updated_at=order.updated_at.isoformat(),
        store=StoreOut(
            id=order.store.id,
            name=order.store.name,
            description=order.store.description,
            address=order.store.address,
            owner=CustomUserOut(
                id=order.store.owner.id,
                username=order.store.owner.username,
                email=order.store.owner.email,
                first_name=order.store.owner.first_name,
                last_name=order.store.owner.last_name,
                phone=order.store.owner.phone,
                phone_verified=order.store.owner.phone_verified,
            ),
            city=RegionOut(
                id=order.store.city.id,
                name=order.store.city.name,
                type=order.store.city.type,
                slug=order.store.city.slug,
            ),
            state=RegionOut(
                id=order.store.state.id,
                name=order.store.state.name,
                type=order.store.state.type,
                slug=order.store.state.slug,
            ),
            country=RegionOut(
                id=order.store.country.id,
                name=order.store.country.name,
                type=order.store.country.type,
                slug=order.store.country.slug,
            ),
            created_at=order.store.created_at.isoformat(),
            updated_at=order.store.updated_at.isoformat(),
        ),
        order_items=[
            OrderItemOut(
                id=item.id,
                product=ProductBaseOut(
                    id=item.product_item.product.id,
                    name=item.product_item.product.name,
                    title=item.product_item.product.title,
                    barcode=item.product_item.product.barcode,
                    slug=item.product_item.product.slug,
                    description=item.product_item.product.description,
                    image_url=item.product_item.product.image.url
                    if item.product_item.product.image
                    else None,
                    unit=item.product_item.product.unit,
                    unit_count=item.product_item.product.unit_count,
                ),
                quantity=item.quantity,
                price_per_unit=item.price_per_unit,
                total_price=item.total_price,
            )
            for item in order.order_items.all()
        ],
    )


# PUT: /api/v2/orders/stores/{store_id}/{order_id}/status
def update_order_status(
    request, store_id: int, order_id: int, status_update: OrderStatusUpdateIn
) -> OrderOut:
    """
    Update the status of an order for a specific store owned by the authenticated user.
    """
    order = get_object_or_404(
        Order.objects.select_related("status_updated_by")
        .prefetch_related("order_items__product_item__product")
        .prefetch_related("store__city", "store__state", "store__country"),
        id=order_id,
        store__id=store_id,
        store__owner=request.auth,
        deleted_at__isnull=True,
    )

    # Validate status
    if status_update.status not in [choice[0] for choice in OrderStatus.choices]:
        raise ValueError(f"Invalid status: {status_update.status}")

    order.status = status_update.status
    order.status_reason = status_update.status_reason
    order.status_updated_by = request.auth
    order.status_updated_at = timezone.now()
    order.save()

    return OrderOut(
        id=order.id,
        wholesaler_id=order.wholesaler.id,
        store_id=order.store.id,
        total_price=order.total_price,
        fees=order.fees,
        deliver_at=order.deliver_at.isoformat() if order.deliver_at else None,
        products_total_price=order.products_total_price,
        products_total_quantity=order.products_total_quantity,
        status=order.status,
        status_reason=order.status_reason,
        status_updated_at=order.status_updated_at.isoformat(),
        status_updated_by=CustomUserOut(
            id=order.status_updated_by.id,
            username=order.status_updated_by.username,
            email=order.status_updated_by.email,
            first_name=order.status_updated_by.first_name,
            last_name=order.status_updated_by.last_name,
            phone=order.status_updated_by.phone,
            phone_verified=order.status_updated_by.phone_verified,
        ),
        created_at=order.created_at.isoformat(),
        updated_at=order.updated_at.isoformat(),
        store=StoreOut(
            id=order.store.id,
            name=order.store.name,
            description=order.store.description,
            address=order.store.address,
            owner=CustomUserOut(
                id=order.store.owner.id,
                username=order.store.owner.username,
                email=order.store.owner.email,
                first_name=order.store.owner.first_name,
                last_name=order.store.owner.last_name,
                phone=order.store.owner.phone,
                phone_verified=order.store.owner.phone_verified,
            ),
            city=RegionOut(
                id=order.store.city.id,
                name=order.store.city.name,
                type=order.store.city.type,
                slug=order.store.city.slug,
            ),
            state=RegionOut(
                id=order.store.state.id,
                name=order.store.state.name,
                type=order.store.state.type,
                slug=order.store.state.slug,
            ),
            country=RegionOut(
                id=order.store.country.id,
                name=order.store.country.name,
                type=order.store.country.type,
                slug=order.store.country.slug,
            ),
            created_at=order.store.created_at.isoformat(),
            updated_at=order.store.updated_at.isoformat(),
        ),
        order_items=[
            OrderItemOut(
                id=item.id,
                product=ProductBaseOut(
                    id=item.product_item.product.id,
                    name=item.product_item.product.name,
                    title=item.product_item.product.title,
                    barcode=item.product_item.product.barcode,
                    slug=item.product_item.product.slug,
                    description=item.product_item.product.description,
                    image_url=item.product_item.product.image.url
                    if item.product_item.product.image
                    else None,
                    unit=item.product_item.product.unit,
                    unit_count=item.product_item.product.unit_count,
                ),
                quantity=item.quantity,
                price_per_unit=item.price_per_unit,
                total_price=item.total_price,
            )
            for item in order.order_items.all()
        ],
    )


# DELETE: /api/v2/orders/stores/{store_id}/{order_id}
def cancel_order(request, store_id: int, order_id: int):
    """
    Cancel an order for a specific store owned by the authenticated user.
    """
    order = get_object_or_404(
        Order,
        id=order_id,
        store__id=store_id,
        store__owner=request.auth,
        deleted_at__isnull=True,
    )
    order.status = OrderStatus.CANCELLED
    order.status_updated_by = request.auth
    order.status_updated_at = timezone.now()
    order.save()
    return {"success": True}
