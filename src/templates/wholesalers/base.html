<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة تحكم التاجر{% endblock %}</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-green: #28a745;
            --primary-green-dark: #1e7e34;
            --primary-green-light: #34ce57;
            --secondary-green: #20c997;
            --light-green: #d4edda;
            --success-green: #155724;
            --warning-orange: #fd7e14;
            --danger-red: #dc3545;
            --dark-gray: #343a40;
            --light-gray: #f8f9fa;
        }

        /* Base styles */
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: var(--light-gray);
            direction: rtl;
            text-align: right;
        }

        /* Navigation */
        .navbar {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
        }

        /* Sidebar */
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 1.5rem 0;
        }

        .sidebar .nav-link {
            color: var(--dark-gray);
            padding: 0.75rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--light-green);
            color: var(--success-green);
            transform: translateX(-5px);
        }

        .sidebar .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
            color: white;
            border-radius: 1rem 1rem 0 0 !important;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        /* Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-green-dark) 0%, var(--primary-green) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-success {
            background-color: var(--secondary-green);
            border-color: var(--secondary-green);
            border-radius: 0.75rem;
        }

        .btn-warning {
            background-color: var(--warning-orange);
            border-color: var(--warning-orange);
            border-radius: 0.75rem;
        }

        .btn-danger {
            background-color: var(--danger-red);
            border-color: var(--danger-red);
            border-radius: 0.75rem;
        }

        /* Forms */
        .form-control,
        .form-select {
            border-radius: 0.75rem;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
        }

        /* Tables */
        .table {
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background-color: var(--light-green);
        }

        /* Stats cards */
        .stats-card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-card .icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stats-card.orders .icon {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%);
        }

        .stats-card.items .icon {
            background: linear-gradient(135deg, var(--secondary-green) 0%, var(--primary-green) 100%);
        }

        .stats-card.sales .icon {
            background: linear-gradient(135deg, var(--warning-orange) 0%, #fd7e14 100%);
        }

        .stats-card.alerts .icon {
            background: linear-gradient(135deg, var(--danger-red) 0%, #c82333 100%);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
                margin-bottom: 1rem;
            }
            
            .container-fluid {
                padding-left: 15px;
                padding-right: 15px;
            }

            .stats-card {
                margin-bottom: 1rem;
            }
        }

        /* Toast messages */
        .toast-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1050;
        }

        .toast {
            border-radius: 0.75rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>

    {% block extra_css %}{% endblock %}
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'wholesaler_dashboard' %}">
                <i class="fas fa-store me-2"></i>
                لوحة تحكم التاجر
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'wholesaler_dashboard' %}">
                            <i class="fas fa-home me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'orders_list' %}">
                            <i class="fas fa-shopping-cart me-1"></i>
                            الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'items_list' %}">
                            <i class="fas fa-boxes me-1"></i>
                            المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ request.wholesaler.title }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#">الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'wholesaler_logout' %}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        {% if messages %}
        <div class="toast-container">
            {% for message in messages %}
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto">إشعار</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body {% if message.tags %}{{ message.tags }}{% endif %}">
                    {{ message }}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Auto-dismiss toasts after 5 seconds
        document.addEventListener('DOMContentLoaded', function () {
            var toasts = document.querySelectorAll('.toast');
            toasts.forEach(function (toast) {
                setTimeout(function () {
                    toast.classList.remove('show');
                }, 5000);
            });

            // Add CSRF token to all HTMX requests
            document.body.addEventListener('htmx:configRequest', function (event) {
                event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>

</html>
