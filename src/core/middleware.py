from django.conf import settings
from django.http import HttpResponseNotFound


class AdminHostRestrictionMiddleware:
    """
    Middleware that restricts access to the admin site based on the hostname.
    In production mode (DEBUG=False), only allows access from internal.tagerplus.com
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if the request is for the admin site
        if request.path.startswith("/admin/"):
            print("Middleware Admin: ", request.get_host())
            # In production mode, check hostname
            if not settings.DEBUG and request.get_host() != "internal.tagerplus.com":
                return HttpResponseNotFound("Not Found.")
        # Check if the request is for the API
        elif request.path.startswith("/api/"):
            print("Middleware API: ", request.get_host())
            # In production mode, check hostname
            if not settings.DEBUG and request.get_host() != "api.tagerplus.com":
                print("Middleware API Not Found: ", request.get_host())
                return HttpResponseNotFound("Not Found.")

        print("Middleware: ", request.get_host())

        return self.get_response(request)
