from django.shortcuts import get_object_or_404
from ninja import Schema
from typing import List, Optional, Literal
from .models import Product, Company, Category, Region
from .schemas import (
    CompanyIn,
    CompanyOut,
    CategoryIn,
    CategoryOut,
    PaginatedResponse,
    ProductIn,
    ProductUpdate,
    ProductOut,
    RegionWithHierarchyOut,
    ExistenceResponse,
)
from ninja.errors import HttpError
from core.custom_router import CustomRouter
import logging
from django.conf import settings
from django.core.cache import cache
import openai
from upstash_vector import Index
from asgiref.sync import sync_to_async
from django.db.models import Q
from django.contrib.postgres.search import SearchQuery, SearchRank
from stores.models import OrderItem
from django.db.models import Sum

# Initialize API
router = CustomRouter(tags=["products"])

logger = logging.getLogger(__name__)


# Company endpoints
@router.post("/companies", response=CompanyOut)
def create_company(request, data: CompanyIn):
    company = Company.objects.create(**data.dict())
    return _company_to_schema(company)


@router.get("/companies", response=List[CompanyOut])
def list_companies(request):
    companies = Company.objects.filter(deleted_at__isnull=True)
    return [_company_to_schema(company) for company in companies]


# Category endpoints
@router.post("/categories", response=CategoryOut)
def create_category(request, data: CategoryIn):
    category = Category.objects.create(**data.dict())
    return _category_to_schema(category)


@router.get("/categories", response=List[CategoryOut])
def list_categories(request):
    categories = Category.objects.filter(deleted_at__isnull=True)
    return [_category_to_schema(category) for category in categories]


@router.get("/categories/{category_id}", response=CategoryOut)
def get_category(request, category_id: int):
    category = get_object_or_404(Category, id=category_id, deleted_at__isnull=True)
    return _category_to_schema(category)


# Product endpoints
@router.post("/products", response=ProductOut)
def create_product(request, data: ProductIn):
    # Extract data excluding relationship IDs and image
    product_data = data.dict(exclude={"company_id", "category_id", "image"})

    # Create product instance
    product = Product.objects.create(**product_data)

    # Handle image upload if provided
    if hasattr(data, "image") and data.image:
        product.image = data.image

    # Set relationships
    if data.company_id:
        company = get_object_or_404(
            Company, id=data.company_id, deleted_at__isnull=True
        )
        product.company = company

    if data.category_id:
        category = get_object_or_404(
            Category, id=data.category_id, deleted_at__isnull=True
        )
        product.category = category

    product.save()
    return _product_to_schema(product)


@router.put("/products/{product_id}", response=ProductOut)
def update_product(request, product_id: int, data: ProductUpdate):
    product = get_object_or_404(Product, id=product_id, deleted_at__isnull=True)

    # Get only fields that were actually provided
    update_data = data.dict(exclude_unset=True)

    # Handle relationships separately
    if "company_id" in update_data:
        company_id = update_data.pop("company_id")
        if company_id:
            product.company = get_object_or_404(
                Company, id=company_id, deleted_at__isnull=True
            )
        else:
            product.company = None

    if "category_id" in update_data:
        category_id = update_data.pop("category_id")
        if category_id:
            product.category = get_object_or_404(
                Category, id=category_id, deleted_at__isnull=True
            )
        else:
            product.category = None

    # Update other fields
    for field, value in update_data.items():
        setattr(product, field, value)

    product.save()
    return _product_to_schema(product)


# Configure OpenAI API
openai.api_key = settings.OPENAI_API_KEY


# Generate embedding for search query
def generate_search_embedding(query_text):
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-large",
            input=query_text,
            dimensions=384,
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error generating search embedding: {str(e)}")
        return None


# Query Upstash Vector DB
def query_vector_db(embedding, top_k=30):
    try:
        # Initialize Upstash Vector client
        index = Index(
            url=settings.UPSTASH_VECTOR_URL,
            token=settings.UPSTASH_VECTOR_TOKEN,
        )

        # Query for similar vectors
        results = index.query(
            vector=embedding,
            top_k=top_k,  # Use the same size as regular pagination
            include_metadata=True,
        )

        # Extract product IDs from results
        product_ids = []
        for result in results:
            if result.metadata and "product_id" in result.metadata:
                product_ids.append(result.metadata["product_id"])

        return product_ids

    except Exception as e:
        # raise HttpError(500, "Internal server error")
        logger.error(f"Error querying Upstash Vector DB: {str(e)}")
        return []


@router.get("/products/", response=PaginatedResponse[ProductOut])
async def list_search_products(
    request,
    search: Optional[str] = None,
    size: int = 10,
    page: int = 1,
    company_id: Optional[int] = None,
    category_id: Optional[int] = None,
):
    # Start with all active products
    queryset = Product.objects.filter(deleted_at__isnull=True)

    # Apply search filter if provided
    if search:
        # Check if we're using PostgreSQL
        is_postgres = (
            settings.DATABASES["default"]["ENGINE"] == "django.db.backends.postgresql"
        )

        # Concurrent search approach - use both PostgreSQL full-text search and vector search
        try:
            search_results = []

            # 1. PostgreSQL full-text search (if using PostgreSQL)
            if is_postgres:
                try:
                    # Create a search query with Arabic configuration
                    search_query = SearchQuery(search, config="arabic")

                    # Log the search query for debugging
                    logger.info(f"PostgreSQL search query: {search_query}")

                    # First try direct match with the search vector
                    pg_queryset = (
                        Product.objects.filter(
                            search_vector=search_query, deleted_at__isnull=True
                        )
                        .annotate(rank=SearchRank("search_vector", search_query))
                        .order_by("-rank")
                    )

                    # Check if we got any results
                    if await pg_queryset.aexists():
                        logger.info("Found results with direct search_vector match")
                        # Add to search results
                        async for p in pg_queryset:
                            search_results.append(p.id)
                    else:
                        # If no results, try a more flexible approach with individual terms
                        logger.info("No direct matches, trying individual terms")
                        # Split the search query into words
                        search_terms = search.split()

                        # Create a combined query with OR logic
                        combined_query = None
                        for term in search_terms:
                            term_query = SearchQuery(term, config="arabic")
                            if combined_query is None:
                                combined_query = term_query
                            else:
                                combined_query = combined_query | term_query

                        if combined_query:
                            # Try with the combined query
                            pg_queryset = (
                                Product.objects.filter(
                                    search_vector=combined_query,
                                    deleted_at__isnull=True,
                                )
                                .annotate(
                                    rank=SearchRank("search_vector", combined_query)
                                )
                                .order_by("-rank")
                            )

                            # Add to search results
                            async for p in pg_queryset:
                                search_results.append(p.id)

                            logger.info(
                                f"Found {len(search_results)} results with combined terms approach"
                            )
                except Exception as pg_error:
                    logger.error(f"Error in PostgreSQL search: {str(pg_error)}")

            # 2. Vector search using Upstash (run concurrently)
            embedding = await sync_to_async(generate_search_embedding)(search)
            if embedding:
                product_ids = await sync_to_async(query_vector_db)(
                    embedding, round(size / 2)
                )  # Fetch more to accommodate pagination

                # Add to search results
                search_results.extend(product_ids)

            # 3. Process search results or fall back to basic search
            if search_results:
                # Use set to remove duplicates while preserving order as much as possible
                unique_ids = []
                seen = set()
                for id in search_results:
                    if id not in seen:
                        unique_ids.append(id)
                        seen.add(id)

                logger.info(f"Found {len(unique_ids)} unique product IDs from search")

                # Get products with the found IDs
                if unique_ids:
                    queryset = Product.objects.filter(
                        id__in=unique_ids, deleted_at__isnull=True
                    )

            # If no results from advanced search or if we're using SQLite, fall back to basic search
            if not search_results or not is_postgres or not await queryset.aexists():
                logger.info("Falling back to basic search with icontains")

                # For SQLite, we need to handle Arabic text search differently
                # Split the search into words for more flexible matching
                search_terms = search.split()

                # Start with an empty query
                combined_query = Q(deleted_at__isnull=True)

                # Add each term as a separate condition
                for term in search_terms:
                    term_query = (
                        Q(name__icontains=term)
                        | Q(title__icontains=term)
                        | Q(description__icontains=term)
                        | Q(company__name__icontains=term)
                        | Q(category__name__icontains=term)
                    )
                    combined_query &= term_query  # All terms must match somewhere

                queryset = Product.objects.filter(combined_query)

                # Log the query for debugging
                logger.info(f"Basic search query: {queryset.query}")

                # If still no results, try a more permissive OR-based search
                if not await queryset.aexists():
                    logger.info(
                        "No results with AND-based search, trying OR-based search"
                    )
                    or_query = Q(deleted_at__isnull=True)

                    for term in search_terms:
                        or_query |= (
                            Q(name__icontains=term)
                            | Q(title__icontains=term)
                            | Q(description__icontains=term)
                            | Q(company__name__icontains=term)
                            | Q(category__name__icontains=term)
                        )

                    queryset = Product.objects.filter(or_query)

        except Exception as e:
            logger.error(f"Error in search: {str(e)}")
            # Fallback to basic search on error
            logger.info("Exception occurred, falling back to simple search")

            # Simple fallback search that should work in any database
            queryset = Product.objects.filter(
                Q(name__icontains=search)
                | Q(title__icontains=search)
                | Q(description__icontains=search)
                | Q(company__name__icontains=search)
                | Q(category__name__icontains=search),
                deleted_at__isnull=True,
            )

    # Apply additional filters if provided
    if company_id:
        queryset = queryset.filter(company_id=company_id)

    if category_id:
        queryset = queryset.filter(category_id=category_id)

    # Get total count for pagination
    total_count = await queryset.acount()

    # Calculate pagination values
    total_pages = max(1, (total_count + size - 1) // size)
    page = max(1, min(page, total_pages))  # Ensure page is valid
    start_idx = (page - 1) * size
    end_idx = start_idx + size

    # prefetch_related to avoid N+1 queries
    queryset = queryset.prefetch_related("company", "category")
    queryset = queryset[start_idx:end_idx]

    # Convert to schema for regular search
    items = []
    async for entry in queryset:
        items.append(_product_to_schema(entry))

    return PaginatedResponse(
        items=items,
        total=total_count,
        page=page,
        size=size,
        pages=total_pages,
    )


# Region endpoints
@router.get("/regions", response=List[RegionWithHierarchyOut])
def list_regions(request):
    """List all regions with hierarchical names"""
    regions = Region.objects.filter(deleted_at__isnull=True)

    result = []
    for region in regions:
        # Build hierarchical name (child - parent - grandparent)
        hierarchical_name = region.name
        current = region

        # Traverse up the parent hierarchy
        while current.parent:
            current = current.parent
            hierarchical_name = f"{hierarchical_name} - {current.name}"

        result.append(
            RegionWithHierarchyOut(
                id=region.id,
                name=region.name,
                type=region.type,
                code=region.code,
                parent_id=region.parent.id if region.parent else None,
                hierarchical_name=hierarchical_name,
            )
        )

    return result


@router.get("/regions/{region_id}", response=RegionWithHierarchyOut)
def get_region(request, region_id: int):
    """Get a specific region with hierarchical name"""
    region = get_object_or_404(Region, id=region_id, deleted_at__isnull=True)

    # Build hierarchical name (child - parent - grandparent)
    hierarchical_name = region.name
    current = region

    # Traverse up the parent hierarchy
    while current.parent:
        current = current.parent
        hierarchical_name = f"{hierarchical_name} - {current.name}"

    return RegionWithHierarchyOut(
        id=region.id,
        name=region.name,
        type=region.type,
        code=region.code,
        hierarchical_name=hierarchical_name,
    )


class SearchInput(Schema):
    query: str
    search_in: Literal["product", "company", "category"]


# Existence check endpoint
@router.post("/exists", response=ExistenceResponse)
def check_existence(request, data: SearchInput):
    # trim query
    query = data.query.strip()
    # Scope-specific existence check
    scope = data.search_in.lower()
    if scope == "product":
        exists = Product.objects.filter(
            name__icontains=query, deleted_at__isnull=True
        ).exists()
        return {"product_exists": exists, "company_id": None, "category_id": None}
    elif scope == "company":
        company = Company.objects.filter(
            name__icontains=query, deleted_at__isnull=True
        ).first()
        return {
            "product_exists": False,
            "company_id": company.id if company else None,
            "category_id": None,
        }
    elif scope == "category":
        category = Category.objects.filter(
            name__icontains=query, deleted_at__isnull=True
        ).first()
        return {
            "product_exists": False,
            "company_id": None,
            "category_id": category.id if category else None,
        }
    else:
        raise HttpError(
            400,
            f"Invalid search_in '{data.search_in}'. Use 'product', 'company' or 'category'.",
        )


# Helper functions to convert models to schemas
def _company_to_schema(company):
    return {
        "id": company.id,
        "name": company.name,
        "title": company.title,
        "slug": company.slug,
    }


def _category_to_schema(category):
    return {
        "id": category.id,
        "name": category.name,
        "title": category.title,
        "slug": category.slug,
    }


def _product_to_schema(product):
    result = {
        "id": product.id,
        "name": product.name,
        "title": product.title,
        "barcode": product.barcode,
        "slug": product.slug,
        "description": product.description,
        "image_url": product.image.url
        if product.image and product.image.name
        else None,
        "company": None,
        "category": None,
        "unit": product.unit,
        "unit_count": product.unit_count,
    }

    if product.company:
        result["company"] = _company_to_schema(product.company)

    if product.category:
        result["category"] = _category_to_schema(product.category)

    return result
