from django.db import models
from django.utils import timezone
from django.contrib.postgres.search import SearchVector<PERSON>ield
from django.contrib.postgres.indexes import GinIndex

from core.utils import RandomFileName


class MeasurementUnit(models.TextChoices):
    PIECE = "PIECE", "Piece"
    KILOGRAM = "KILOGRAM", "Kilogram (kg)"
    GRAM = "GRAM", "Gram (g)"
    LITER = "LITER", "Liter (L)"
    MILLILITER = "MILLILITER", "Milliliter (mL)"
    METER = "METER", "Meter (m)"
    CENTIMETER = "CENTIMETER", "Centimeter (cm)"
    BOX = "BOX", "Box"
    CARTON = "CARTON", "Carton"
    PACK = "PACK", "Pack"
    BOTTLE = "BOTTLE", "Bottle"
    CAN = "CAN", "Can"
    DOZEN = "DOZEN", "Dozen"
    PAIR = "PAIR", "Pair"
    OTHER = "OTHER", "Other"


class Company(models.Model):
    name = models.CharField(max_length=255, help_text="Company name, e.g. Apple Inc.")
    title = models.CharField(
        max_length=255, help_text="Company title, e.g. Apple Inc. (System name)"
    )
    slug = models.SlugField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Company"
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Company, self).delete(*args, **kwargs)


class Category(models.Model):
    name = models.CharField(max_length=255, help_text="Category name, e.g. Electronics")
    title = models.CharField(
        max_length=255, help_text="Category title, e.g. Electronics (System name)"
    )
    slug = models.SlugField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Category"
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Category, self).delete(*args, **kwargs)


class RegionType(models.TextChoices):
    COUNTRY = "COUNTRY", "Country"
    STATE = "STATE", "State/Province"
    CITY = "CITY", "City"
    DISTRICT = "DISTRICT", "District"
    OTHER = "OTHER", "Other"


class Region(models.Model):
    name = models.CharField(
        max_length=255, help_text="Region name, e.g. USA, California, San Francisco"
    )
    type = models.CharField(
        max_length=20,
        choices=RegionType.choices,
        default=RegionType.OTHER,
        help_text="Type of region",
    )
    parent = models.ForeignKey(
        "self",
        null=True,
        blank=True,
        related_name="children",
        on_delete=models.CASCADE,
        help_text="Parent region. E.g. USA is parent of California, California is parent of San Francisco",
    )
    code = models.CharField(
        max_length=50,
        blank=True,
        help_text="Optional region code (e.g. ISO country code, state abbreviation)",
    )
    slug = models.SlugField(unique=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Region"
        verbose_name_plural = "Regions"
        indexes = [
            models.Index(fields=["parent", "type"]),
            models.Index(fields=["type", "is_active"]),
            models.Index(fields=["code"]),
        ]
        constraints = [
            # Ensure a region can't be its own parent
            models.CheckConstraint(
                check=~models.Q(id=models.F("parent")), name="prevent_self_parent"
            ),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

    def get_full_path(self):
        """Return the full path from country to this region"""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name

    @property
    def country(self):
        """Get the country for this region"""
        if self.type == RegionType.COUNTRY:
            return self

        current = self
        while current.parent:
            current = current.parent
            if current.type == RegionType.COUNTRY:
                return current
        return None

    @property
    def state(self):
        """Get the state/province for this region"""
        if self.type == RegionType.STATE:
            return self

        if self.type == RegionType.COUNTRY:
            return None

        current = self
        while current.parent:
            current = current.parent
            if current.type == RegionType.STATE:
                return current
            if current.type == RegionType.COUNTRY:
                # We reached country level without finding a state
                return None
        return None

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Region, self).delete(*args, **kwargs)


class Product(models.Model):
    items_count = models.IntegerField(
        default=0, help_text="Number of items in this product"
    )

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name="products",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    barcode = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(default="")
    image = models.ImageField(
        upload_to=RandomFileName("products"),
        null=True,
        blank=True,
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name="products",
        null=True,
        blank=True,
    )
    # Unit information
    unit = models.CharField(
        max_length=50,
        choices=MeasurementUnit.choices,
        default=MeasurementUnit.PIECE,
        help_text="Unit of measurement for this product",
    )
    unit_count = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=1.0,
        help_text="Number of units in this product",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    # Full-text search vector field
    search_vector = SearchVectorField(null=True, blank=True)

    class Meta:
        verbose_name = "Product"
        verbose_name_plural = "Products"
        indexes = [
            GinIndex(fields=["search_vector"], name="product_search_vector_idx"),
        ]

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Product, self).delete(*args, **kwargs)
