"""
Authentication views for login and signup and OTP verification
"""

import json
import logging
import random
import re
from datetime import datetime, timedelta, timezone

import httpx
import jwt
from core import settings
from django.core.exceptions import ValidationError
from django_redis import get_redis_connection
from ninja.errors import HttpError
from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional

from accounts.middleware import AuthMiddleware
from accounts.models import CustomUser
from core.custom_router import CustomRouter

# Configure proper logger
logger = logging.getLogger(__name__)

# Create router for auth endpoints
router = CustomRouter(tags=["auth"])

# Constants
OTP_EXPIRY_SECONDS = 60 * 10  # 10 minutes
JWT_EXPIRY_DAYS = 7  # Token valid for 7 days
REDIS_OTP_PREFIX = "otp:"


# Request and response schemas
class SignupRequest(BaseModel):
    name: str = Field(..., description="User's full name", min_length=2, max_length=100)
    phone: str = Field(
        ..., description="Phone number with country code (e.g., +************)"
    )
    password: str = Field(
        ..., min_length=8, description="User password (min 8 characters)"
    )
    email: Optional[EmailStr] = Field(None, description="Optional email address")

    @field_validator("phone")
    def validate_phone(cls, v):
        if not v.startswith("+"):
            raise ValueError(
                "Phone number must include country code (e.g., +************)"
            )

        # More comprehensive phone validation with regex
        if not re.match(r"^\+[0-9]{10,15}$", v):
            raise ValueError("Invalid phone number format")
        return v

    @field_validator("password")
    def validate_password_complexity(cls, v):
        # Check for password complexity
        if (
            len(v) < 8
            or not re.search(r"[A-Z]", v)
            or not re.search(r"[a-z]", v)
            or not re.search(r"[0-9]", v)
        ):
            raise ValueError(
                "Password must be at least 8 characters and include uppercase, lowercase, and numbers"
            )
        return v


class LoginRequest(BaseModel):
    phone: str = Field(
        ..., description="Phone number with country code (e.g., +************)"
    )
    password: Optional[str] = Field(None, description="User password")
    otp: Optional[str] = Field(None, description="OTP received on phone")


class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[str] = None


class SignupResponse(BaseModel):
    success: bool = True
    user_id: int
    message: str
    channel: str


class TrustedSignupResponse(BaseModel):
    success: bool = True
    token: str
    user_id: int
    phone: str


class LoginResponse(BaseModel):
    success: bool = True
    token: str
    user_id: int
    phone: str
    is_phone_verified: bool


class UserResponse(BaseModel):
    """Response schema for user data"""

    id: int
    username: str
    email: Optional[str] = None
    phone: str
    phone_verified: bool
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    date_joined: datetime


class UserUpdateRequest(BaseModel):
    """Request schema for updating user data"""

    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    email: Optional[str] = Field(None, description="User's email address")
    phone: Optional[str] = Field(
        None, description="Phone number with country code (e.g., +************)"
    )

    @field_validator("phone")
    def validate_phone(cls, v):
        if v is None:
            return v

        if not v.startswith("+"):
            raise ValueError(
                "Phone number must include country code (e.g., +************)"
            )

        # More comprehensive phone validation with regex
        if not re.match(r"^\+[0-9]{10,15}$", v):
            raise ValueError("Invalid phone number format")
        return v


async def generate_jwt_token(user_id: int) -> str:
    """Generate a secure JWT token with expiration time"""
    now = datetime.now(timezone.utc)
    payload = {
        "user_id": user_id,
        "exp": now + timedelta(days=JWT_EXPIRY_DAYS),
        "iat": now,
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")


async def store_otp_data(phone: str, transaction_data: dict, user_id: int) -> None:
    """Store OTP data in Redis with proper error handling"""
    try:
        redis = get_redis_connection("default")
        data_to_store = {
            "transaction_id": transaction_data["transaction_id"],
            "transaction_req_id": transaction_data["transaction_req_id"],
            "phone": phone,
            "user_id": user_id,
            "created_at": datetime.now(timezone.utc).isoformat(),
        }

        # If we're in debug mode and have a debug OTP, store it
        if settings.DEBUG and "debug_otp" in transaction_data:
            data_to_store["debug_otp"] = transaction_data["debug_otp"]

        redis.set(
            f"{REDIS_OTP_PREFIX}{phone}",
            json.dumps(data_to_store),
            ex=OTP_EXPIRY_SECONDS,
        )
    except Exception as e:
        logger.error(f"Redis error while storing OTP data: {str(e)}")
        raise HttpError(500, "Internal server error")


async def request_otp(phone: str) -> dict:
    """Request OTP from external service with error handling

    In debug mode, this will generate a mock OTP and store it in Redis
    instead of calling the external service.
    """
    # Check if we're in debug mode
    if settings.DEBUG:
        # Generate a random 6-digit OTP
        otp_code = "".join([str(random.randint(0, 9)) for _ in range(6)])

        # Store the OTP in Redis with the phone as key
        redis = get_redis_connection("default")
        redis.set(f"debug_otp:{phone}", otp_code, ex=OTP_EXPIRY_SECONDS)

        # Log the OTP for debugging purposes
        logger.info(f"DEBUG MODE: Generated OTP {otp_code} for {phone}")

        # Print the OTP to the console for easier visibility during development
        print("\n\n===================================================")
        print(f"DEBUG MODE: OTP CODE FOR {phone}: {otp_code}")
        print("==================================================\n\n")

        # Return mock transaction data
        mock_transaction_id = f"mock-{random.randint(10000, 99999)}"
        mock_req_id = f"mock-req-{random.randint(10000, 99999)}"

        return {
            "transaction_id": mock_transaction_id,
            "transaction_req_id": mock_req_id,
            "channel": "debug",
            "debug_otp": otp_code,  # Include the OTP in the response for debugging
        }

    # Production mode - use the real service
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                "https://api.akedly.io/api/v1/transactions",
                json={
                    "APIKey": settings.AKEDLY_API_KEY,
                    "pipelineID": settings.AKEDLY_PIPELINE_ID,
                    "verificationAddress": {
                        "phoneNumber": phone,
                    },
                },
            )

            response_data = response.json()
            if response.status_code != 200 or response_data.get("status") != "success":
                logger.error(f"OTP request failed: {response.text}")
                raise HttpError(400, "Failed to request OTP")

            transaction_id = response_data["data"]["transactionID"]

            # Activate transaction
            activate_response = await client.post(
                f"https://api.akedly.io/api/v1/transactions/activate/{transaction_id}",
            )

            activate_data = activate_response.json()
            if (
                activate_response.status_code != 200
                or activate_data.get("status") != "success"
            ):
                logger.error(
                    f"Failed to activate transaction: {activate_response.text}"
                )
                raise HttpError(400, "Failed to activate OTP transaction")

            # chceck if whatsapp if so, thne check if it's true
            # Initialize default channel
            channel = "sms"

            # Check for the whatsapp key (case insensitive) in the data
            for key in activate_data["data"].keys():
                if key.lower() == "whatsapp" and activate_data["data"][key] is True:
                    channel = "whatsapp"
                    break

            return {
                "transaction_id": transaction_id,
                "transaction_req_id": activate_data["data"]["_id"],
                "channel": channel,
            }
    except httpx.RequestError as e:
        logger.error(f"OTP service connection error: {str(e)}")
        raise HttpError(503, "OTP service unavailable")


@router.post(
    "/signup", response={201: SignupResponse, 400: ErrorResponse, 500: ErrorResponse}
)
async def signup(request, data: SignupRequest):
    """
    Register a new user and initiate OTP verification

    This endpoint:
    1. Creates a new user account
    2. Requests and sends an OTP for phone verification
    3. Returns user details for verification
    """
    try:
        # Check if user exists
        if await CustomUser.objects.filter(phone=data.phone).aexists():
            return 400, {"error": "User already exists", "error_code": "USER_EXISTS"}

        # Create user with proper error handling
        try:
            user = await CustomUser.objects.acreate_user(
                username=data.name,
                email=data.email,
                phone=data.phone,
                password=data.password,
            )
        except ValidationError as e:
            logger.warning(f"User creation validation error: {str(e)}")
            return 400, {"error": str(e), "error_code": "VALIDATION_ERROR"}

        # Request OTP
        try:
            transaction_data = await request_otp(data.phone)
            await store_otp_data(data.phone, transaction_data, user.id)
        except HttpError as e:
            # Clean up by deleting the user if OTP request fails
            await user.adelete()
            return e.status_code, {"error": str(e.message), "error_code": "OTP_ERROR"}

        return 201, {
            "user_id": user.id,
            "channel": transaction_data["channel"],
            "message": "User created successfully, please verify your phone number",
        }
    except Exception as e:
        logger.exception(f"Unexpected error in signup: {str(e)}")
        return 500, {"error": "Internal server error", "error_code": "SERVER_ERROR"}


@router.post(
    "/signup/trusted",
    response={201: TrustedSignupResponse, 400: ErrorResponse, 500: ErrorResponse},
    auth=None,
)
async def signup_trusted(request, data: SignupRequest):
    """
    Register a new user without phone verification (for trusted users only)

    This endpoint:
    1. Creates a new user account
    2. Marks the phone as verified
    3. Returns JWT token and user details
    """
    try:
        # check user name already exist
        if await CustomUser.objects.filter(username=data.name).aexists():
            return 400, {
                "error": "User name already exists",
                "error_code": "USER_EXISTS",
            }
        # Check if user exists
        if await CustomUser.objects.filter(phone=data.phone).aexists():
            return 400, {"error": "User already exists", "error_code": "USER_EXISTS"}

        # Create user with phone already verified
        try:
            user = await CustomUser.objects.acreate_user(
                username=data.name,
                email=data.email,
                phone=data.phone,
                password=data.password,
                phone_verified=True,  # Mark phone as verified
            )
        except ValidationError as e:
            logger.warning(f"User creation validation error: {str(e)}")
            return 400, {"error": str(e), "error_code": "VALIDATION_ERROR"}

        except Exception as e:
            logger.exception(f"Unexpected error creating user: {str(e)}")
            return 500, {"error": "Internal server error", "error_code": "SERVER_ERROR"}

        # Generate JWT token
        token = await generate_jwt_token(user.id)

        return 201, {
            "token": token,
            "user_id": user.id,
            "phone": user.phone,
        }
    except Exception as e:
        logger.exception(f"Unexpected error in trusted signup: {str(e)}")
        return 500, {"error": "Internal server error", "error_code": "SERVER_ERROR"}


@router.post("/verify/verify-phone", response={200: LoginResponse, 400: ErrorResponse})
async def verify_phone(request, data: LoginRequest):
    """
    Verify phone number with OTP

    This endpoint:
    1. Verifies the OTP
    2. Marks the phone as verified
    3. Returns JWT token and user details
    """
    # get user from db
    user = await CustomUser.objects.filter(phone=data.phone).afirst()
    if not user:
        return 400, {"error": "User not found"}

    # get otp data from redis
    redis = get_redis_connection("default")
    otp_data = redis.get(f"{REDIS_OTP_PREFIX}{data.phone}")
    if otp_data:
        otp_data = json.loads(otp_data)
    if not otp_data:
        return 400, {"error": "No active OTP verification found"}

    # Check if we're in debug mode
    if settings.DEBUG:
        # In debug mode, check if the OTP matches the one we stored
        if "debug_otp" in otp_data:
            # Verify the OTP directly
            if data.otp != otp_data["debug_otp"]:
                return 400, {"error": "Invalid OTP"}

            logger.info(f"DEBUG MODE: OTP verified for {data.phone}")
            print("\n\n===================================================")
            print(f"DEBUG MODE: OTP VERIFIED for {data.phone}")
            print("==================================================\n\n")
        else:
            # Check if there's a debug OTP stored separately
            debug_otp = redis.get(f"debug_otp:{data.phone}")
            if debug_otp and debug_otp.decode() == data.otp:
                logger.info(
                    f"DEBUG MODE: OTP verified for {data.phone} using separate key"
                )
                print("\n\n===================================================")
                print(f"DEBUG MODE: OTP VERIFIED for {data.phone} (separate key)")
                print("==================================================\n\n")
            else:
                # If we can't find a debug OTP, reject the verification
                # We don't fall back to Akedly in debug mode
                return 400, {"error": "Invalid OTP"}
    else:
        # Production mode - verify with the external service
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"https://api.akedly.io/api/v1/transactions/verify/{otp_data['transaction_req_id']}",
                json={"otp": data.otp},
            )
            response_data = response.json()
            if "status" not in response_data or response_data["status"] != "success":
                return 400, {"error": "Invalid OTP"}

    # mark phone as verified
    user.phone_verified = True
    await user.asave()

    # generate token and return
    token = await generate_jwt_token(user.id)

    # Clean up Redis
    redis.delete(f"{REDIS_OTP_PREFIX}{data.phone}")
    if settings.DEBUG:
        redis.delete(f"debug_otp:{data.phone}")

    return 200, {
        "token": token,
        "user_id": user.id,
        "phone": user.phone,
        "is_phone_verified": user.phone_verified,
    }


@router.post("/login", response={200: LoginResponse, 401: ErrorResponse})
async def login(request, data: LoginRequest):
    """
    Login with phone number and password

    This endpoint:
    1. Authenticates the user with phone and password
    2. Checks if phone is verified
    3. Returns JWT token and user details
    """
    user = await CustomUser.objects.filter(phone=data.phone).afirst()
    if not user or not user.check_password(data.password):
        return 401, {"error": "Invalid credentials"}

    if not user.phone_verified:
        return 401, {"error": "Phone not verified"}

    token = await generate_jwt_token(user.id)
    return 200, {
        "token": token,
        "user_id": user.id,
        "phone": user.phone,
        "is_phone_verified": user.phone_verified,
    }


@router.get(
    "/me", response={200: UserResponse, 401: ErrorResponse}, auth=AuthMiddleware
)
async def get_current_user(request):
    """
    Get the current authenticated user's data

    This endpoint:
    1. Uses the JWT token to identify the user
    2. Returns the user's profile data
    """
    # The user is already attached to the request by the AuthMiddleware
    user = request.user

    return 200, {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "phone": user.phone,
        "phone_verified": user.phone_verified,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_active": user.is_active,
        "date_joined": user.date_joined,
    }


@router.put(
    "/me",
    response={200: UserResponse, 400: ErrorResponse, 401: ErrorResponse},
    auth=AuthMiddleware,
)
async def update_current_user(request, data: UserUpdateRequest):
    """
    Update the current authenticated user's data

    This endpoint:
    1. Uses the JWT token to identify the user
    2. Updates the user's profile with the provided data
    3. Returns the updated user profile

    Fields that can be updated:
    - first_name
    - last_name
    - email
    - phone (requires verification)
    """
    # The user is already attached to the request by the AuthMiddleware
    user = request.user

    # Update fields if provided
    if data.first_name is not None:
        user.first_name = data.first_name

    if data.last_name is not None:
        user.last_name = data.last_name

    if data.email is not None:
        user.email = data.email

    # Handle phone number update - requires special handling
    if data.phone is not None and data.phone != user.phone:
        # Check if phone number is already in use
        if await CustomUser.objects.filter(phone=data.phone).aexists():
            return 400, {
                "error": "Phone number already in use",
                "error_code": "PHONE_EXISTS",
            }

        # Update phone and mark as unverified
        user.phone = data.phone
        user.phone_verified = False

        # TODO: Implement OTP verification for the new phone number
        # For now, we'll just update the phone and mark it as unverified

    # Save the updated user
    try:
        await user.asave()
    except ValidationError as e:
        logger.warning(f"User update validation error: {str(e)}")
        return 400, {"error": str(e), "error_code": "VALIDATION_ERROR"}
    except Exception as e:
        logger.exception(f"Unexpected error updating user: {str(e)}")
        return 400, {"error": "Failed to update user", "error_code": "UPDATE_ERROR"}

    # Return the updated user data
    return 200, {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "phone": user.phone,
        "phone_verified": user.phone_verified,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "is_active": user.is_active,
        "date_joined": user.date_joined,
    }
