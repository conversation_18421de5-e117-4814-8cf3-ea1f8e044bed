from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import ensure_csrf_cookie
from django.template.loader import render_to_string
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from django.utils import timezone
from datetime import datetime, timedelta
from functools import wraps
import json

from accounts.models import CustomUser
from .models import Wholesaler, Item
from products.models import Product, Company, Category
from stores.models import Order, OrderItem
from products.views import list_search_products


def wholesaler_required(view_func):
    """Decorator to ensure user is a wholesaler"""

    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect("wholesaler_login")

        try:
            wholesaler = Wholesaler.objects.get(
                user=request.user, deleted_at__isnull=True
            )
            request.wholesaler = wholesaler
        except Wholesaler.DoesNotExist:
            messages.error(request, "ليس لديك صلاحية للوصول إلى لوحة التحكم")
            return redirect("wholesaler_login")

        return view_func(request, *args, **kwargs)

    return _wrapped_view


@require_http_methods(["GET", "POST"])
def wholesaler_login(request):
    """Login view for wholesalers"""
    if request.user.is_authenticated:
        try:
            Wholesaler.objects.get(user=request.user, deleted_at__isnull=True)
            return redirect("wholesaler_dashboard")
        except Wholesaler.DoesNotExist:
            logout(request)

    if request.method == "POST":
        phone = request.POST.get("phone")
        password = request.POST.get("password")

        if phone and password:
            try:
                user = CustomUser.objects.get(phone=phone, phone_verified=True)
                if user.check_password(password):
                    # Check if user is a wholesaler
                    try:
                        wholesaler = Wholesaler.objects.get(
                            user=user, deleted_at__isnull=True
                        )
                        login(request, user)
                        messages.success(request, f"مرحباً {wholesaler.title}")
                        return redirect("wholesaler_dashboard")
                    except Wholesaler.DoesNotExist:
                        messages.error(
                            request, "ليس لديك صلاحية للوصول إلى لوحة التحكم"
                        )
                else:
                    messages.error(request, "رقم الهاتف أو كلمة المرور غير صحيحة")
            except CustomUser.DoesNotExist:
                messages.error(request, "رقم الهاتف أو كلمة المرور غير صحيحة")
        else:
            messages.error(request, "يرجى إدخال رقم الهاتف وكلمة المرور")

    return render(request, "wholesalers/auth/login.html")


@login_required
def wholesaler_logout(request):
    """Logout view for wholesalers"""
    logout(request)
    messages.success(request, "تم تسجيل الخروج بنجاح")
    return redirect("wholesaler_login")


@wholesaler_required
def wholesaler_dashboard(request):
    """Main dashboard view for wholesalers"""
    wholesaler = request.wholesaler

    # Get dashboard statistics
    total_orders = Order.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).count()
    pending_orders = Order.objects.filter(
        wholesaler=wholesaler, status="pending", deleted_at__isnull=True
    ).count()
    total_items = Item.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).count()
    low_stock_items = Item.objects.filter(
        wholesaler=wholesaler, inventory_count__lt=10, deleted_at__isnull=True
    ).count()

    # Recent orders
    recent_orders = Order.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).order_by("-created_at")[:5]

    # Sales data for the last 7 days
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=6)

    daily_sales = []
    for i in range(7):
        date = start_date + timedelta(days=i)
        sales = (
            Order.objects.filter(
                wholesaler=wholesaler, created_at__date=date, deleted_at__isnull=True
            ).aggregate(total=Sum("total_price"))["total"]
            or 0
        )
        daily_sales.append({"date": date.strftime("%Y-%m-%d"), "sales": float(sales)})

    context = {
        "wholesaler": wholesaler,
        "total_orders": total_orders,
        "pending_orders": pending_orders,
        "total_items": total_items,
        "low_stock_items": low_stock_items,
        "recent_orders": recent_orders,
        "daily_sales": json.dumps(daily_sales),
    }

    return render(request, "wholesalers/dashboard/main.html", context)


@wholesaler_required
def orders_list(request):
    """List all orders for the wholesaler"""
    wholesaler = request.wholesaler

    # Get filter parameters
    status_filter = request.GET.get("status", "")
    search_query = request.GET.get("search", "")
    date_from = request.GET.get("date_from", "")
    date_to = request.GET.get("date_to", "")

    # Base queryset
    orders = Order.objects.filter(wholesaler=wholesaler, deleted_at__isnull=True)

    # Apply filters
    if status_filter:
        orders = orders.filter(status=status_filter)

    if search_query:
        orders = orders.filter(
            Q(store__name__icontains=search_query)
            | Q(store__owner__username__icontains=search_query)
            | Q(id__icontains=search_query)
        )

    if date_from:
        try:
            from_date = datetime.strptime(date_from, "%Y-%m-%d").date()
            orders = orders.filter(created_at__date__gte=from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, "%Y-%m-%d").date()
            orders = orders.filter(created_at__date__lte=to_date)
        except ValueError:
            pass

    # Order by creation date (newest first)
    orders = orders.order_by("-created_at")

    # Pagination
    paginator = Paginator(orders, 20)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    # Get order statistics
    total_orders = Order.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).count()
    pending_orders = Order.objects.filter(
        wholesaler=wholesaler, status="pending", deleted_at__isnull=True
    ).count()
    completed_orders = Order.objects.filter(
        wholesaler=wholesaler, status="delivered", deleted_at__isnull=True
    ).count()

    context = {
        "wholesaler": wholesaler,
        "page_obj": page_obj,
        "orders": page_obj.object_list,
        "total_orders": total_orders,
        "pending_orders": pending_orders,
        "completed_orders": completed_orders,
        "status_filter": status_filter,
        "search_query": search_query,
        "date_from": date_from,
        "date_to": date_to,
    }

    # For HTMX requests, return only the orders list portion
    if request.headers.get("HX-Request"):
        return render(request, "wholesalers/orders/partials/orders_list.html", context)

    return render(request, "wholesalers/orders/list.html", context)


@wholesaler_required
def order_detail(request, order_id):
    """View detailed information about a specific order"""
    wholesaler = request.wholesaler
    order = get_object_or_404(
        Order, id=order_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    # Get order items
    order_items = OrderItem.objects.filter(order=order).select_related("item__product")

    context = {
        "wholesaler": wholesaler,
        "order": order,
        "order_items": order_items,
    }

    return render(request, "wholesalers/orders/detail.html", context)


@wholesaler_required
@require_http_methods(["POST"])
def update_order_status(request, order_id):
    """Update the status of an order"""
    wholesaler = request.wholesaler
    order = get_object_or_404(
        Order, id=order_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    new_status = request.POST.get("status")
    valid_statuses = ["pending", "processing", "shipped", "delivered", "cancelled"]

    if new_status in valid_statuses:
        old_status = order.status
        order.status = new_status
        order.save()

        # Create status message
        status_names = {
            "pending": "في الانتظار",
            "processing": "قيد المعالجة",
            "shipped": "تم الشحن",
            "delivered": "تم التسليم",
            "cancelled": "ملغي",
        }

        messages.success(
            request,
            f'تم تحديث حالة الطلب #{order.id} من "{status_names.get(old_status, old_status)}" إلى "{status_names.get(new_status, new_status)}"',
        )

        # For HTMX requests, return updated order row
        if request.headers.get("HX-Request"):
            context = {"order": order}
            return render(
                request, "wholesalers/orders/partials/order_row.html", context
            )
    else:
        messages.error(request, "حالة الطلب غير صحيحة")

    return redirect("orders_list")


@wholesaler_required
def items_list(request):
    """List all items/inventory for the wholesaler"""
    wholesaler = request.wholesaler

    # Get filter parameters
    search_query = request.GET.get("search", "")
    company_filter = request.GET.get("company", "")
    category_filter = request.GET.get("category", "")
    stock_filter = request.GET.get("stock", "")

    # Base queryset
    items = Item.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).select_related("product__company", "product__category")

    # Apply filters
    if search_query:
        items = items.filter(
            Q(product__name__icontains=search_query)
            | Q(product__title__icontains=search_query)
            | Q(product__barcode__icontains=search_query)
        )

    if company_filter:
        items = items.filter(product__company_id=company_filter)

    if category_filter:
        items = items.filter(product__category_id=category_filter)

    if stock_filter == "low":
        items = items.filter(inventory_count__lt=10)
    elif stock_filter == "out":
        items = items.filter(inventory_count=0)
    elif stock_filter == "available":
        items = items.filter(inventory_count__gt=0)

    # Order by creation date (newest first)
    items = items.order_by("-created_at")

    # Pagination
    paginator = Paginator(items, 20)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    # Get filter options
    companies = Company.objects.filter(deleted_at__isnull=True).order_by("name")
    categories = Category.objects.filter(deleted_at__isnull=True).order_by("name")

    # Get statistics
    total_items = Item.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).count()
    low_stock_items = Item.objects.filter(
        wholesaler=wholesaler, inventory_count__lt=10, deleted_at__isnull=True
    ).count()
    out_of_stock_items = Item.objects.filter(
        wholesaler=wholesaler, inventory_count=0, deleted_at__isnull=True
    ).count()

    context = {
        "wholesaler": wholesaler,
        "page_obj": page_obj,
        "items": page_obj.object_list,
        "companies": companies,
        "categories": categories,
        "total_items": total_items,
        "low_stock_items": low_stock_items,
        "out_of_stock_items": out_of_stock_items,
        "search_query": search_query,
        "company_filter": company_filter,
        "category_filter": category_filter,
        "stock_filter": stock_filter,
    }

    # For HTMX requests, return only the items list portion
    if request.headers.get("HX-Request"):
        return render(request, "wholesalers/items/partials/items_list.html", context)

    return render(request, "wholesalers/items/list.html", context)


@wholesaler_required
def item_detail(request, item_id):
    """View detailed information about a specific item"""
    wholesaler = request.wholesaler
    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    context = {
        "wholesaler": wholesaler,
        "item": item,
    }

    return render(request, "wholesalers/items/detail.html", context)


@wholesaler_required
@require_http_methods(["GET", "POST"])
def item_edit(request, item_id):
    """Edit an existing item"""
    wholesaler = request.wholesaler
    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    if request.method == "POST":
        # Update item fields
        base_price = request.POST.get("base_price")
        inventory_count = request.POST.get("inventory_count")
        minimum_order_quantity = request.POST.get("minimum_order_quantity")
        maximum_order_quantity = request.POST.get("maximum_order_quantity")

        try:
            if base_price:
                item.base_price = float(base_price)
            if inventory_count:
                item.inventory_count = int(inventory_count)
            if minimum_order_quantity:
                item.minimum_order_quantity = int(minimum_order_quantity)
            if maximum_order_quantity:
                item.maximum_order_quantity = (
                    int(maximum_order_quantity) if maximum_order_quantity else None
                )

            item.save()
            messages.success(request, f'تم تحديث المنتج "{item.product.name}" بنجاح')

            # For HTMX requests, return updated item row
            if request.headers.get("HX-Request"):
                context = {"item": item}
                return render(
                    request, "wholesalers/items/partials/item_row.html", context
                )

            return redirect("items_list")

        except (ValueError, TypeError) as e:
            messages.error(request, "يرجى التأكد من صحة البيانات المدخلة")

    context = {
        "wholesaler": wholesaler,
        "item": item,
    }

    return render(request, "wholesalers/items/edit.html", context)


# Multi-stage item creation workflow
@wholesaler_required
@require_http_methods(["GET", "POST"])
def item_create_step1(request):
    """Step 1: Company selection (optional)"""
    wholesaler = request.wholesaler

    if request.method == "POST":
        company_id = request.POST.get("company_id")

        # Store company selection in session
        request.session["item_creation"] = {
            "company_id": company_id if company_id else None,
            "step": 1,
        }

        return redirect("item_create_step2")

    # Get all companies
    companies = Company.objects.filter(deleted_at__isnull=True).order_by("name")

    context = {
        "wholesaler": wholesaler,
        "companies": companies,
        "step": 1,
        "total_steps": 3,
    }

    return render(request, "wholesalers/items/create/step1.html", context)


@wholesaler_required
@require_http_methods(["GET", "POST"])
def item_create_step2(request):
    """Step 2: Product selection with search functionality"""
    wholesaler = request.wholesaler

    # Check if step 1 was completed
    if "item_creation" not in request.session:
        return redirect("item_create_step1")

    if request.method == "POST":
        product_id = request.POST.get("product_id")

        if not product_id:
            messages.error(request, "يرجى اختيار منتج")
            return redirect("item_create_step2")

        # Check if product already exists for this wholesaler
        existing_item = Item.objects.filter(
            wholesaler=wholesaler, product_id=product_id, deleted_at__isnull=True
        ).first()

        if existing_item:
            messages.error(request, "هذا المنتج موجود بالفعل في مخزونك")
            return redirect("item_create_step2")

        # Store product selection in session
        request.session["item_creation"]["product_id"] = product_id
        request.session["item_creation"]["step"] = 2
        request.session.modified = True

        return redirect("item_create_step3")

    # Get search parameters
    search_query = request.GET.get("search", "")
    company_filter = request.session["item_creation"].get("company_id")

    # Use the existing list_search_products functionality
    products = Product.objects.filter(deleted_at__isnull=True)

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query)
            | Q(title__icontains=search_query)
            | Q(barcode__icontains=search_query)
        )

    if company_filter:
        products = products.filter(company_id=company_filter)

    # Exclude products already in wholesaler's inventory
    existing_product_ids = Item.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    ).values_list("product_id", flat=True)

    products = products.exclude(id__in=existing_product_ids)

    # Pagination
    paginator = Paginator(products, 12)
    page_number = request.GET.get("page")
    page_obj = paginator.get_page(page_number)

    context = {
        "wholesaler": wholesaler,
        "page_obj": page_obj,
        "products": page_obj.object_list,
        "search_query": search_query,
        "selected_company": company_filter,
        "step": 2,
        "total_steps": 3,
    }

    # For HTMX requests, return only the products list
    if request.headers.get("HX-Request"):
        return render(
            request, "wholesalers/items/create/partials/products_list.html", context
        )

    return render(request, "wholesalers/items/create/step2.html", context)


@wholesaler_required
@require_http_methods(["GET", "POST"])
def item_create_step3(request):
    """Step 3: Complete item details form"""
    wholesaler = request.wholesaler

    # Check if previous steps were completed
    if (
        "item_creation" not in request.session
        or "product_id" not in request.session["item_creation"]
    ):
        return redirect("item_create_step1")

    product_id = request.session["item_creation"]["product_id"]
    product = get_object_or_404(Product, id=product_id, deleted_at__isnull=True)

    if request.method == "POST":
        # Get form data
        base_price = request.POST.get("base_price")
        inventory_count = request.POST.get("inventory_count", 0)
        minimum_order_quantity = request.POST.get("minimum_order_quantity", 1)
        maximum_order_quantity = request.POST.get("maximum_order_quantity")

        try:
            # Create the item
            item = Item.objects.create(
                wholesaler=wholesaler,
                product=product,
                base_price=float(base_price),
                inventory_count=int(inventory_count),
                minimum_order_quantity=int(minimum_order_quantity),
                maximum_order_quantity=int(maximum_order_quantity)
                if maximum_order_quantity
                else None,
            )

            # Clear session data
            del request.session["item_creation"]
            request.session.modified = True

            messages.success(request, f'تم إضافة المنتج "{product.name}" بنجاح')
            return redirect("items_list")

        except (ValueError, TypeError) as e:
            messages.error(request, "يرجى التأكد من صحة البيانات المدخلة")

    context = {
        "wholesaler": wholesaler,
        "product": product,
        "step": 3,
        "total_steps": 3,
    }

    return render(request, "wholesalers/items/create/step3.html", context)
